# XAU 套利交易系統

## 📋 專案概述

這是一個自動化的黃金（XAU）套利交易系統，專門監控 **Bybit XAUTUSDT 永續合約** 和 **Bybit MT5 XAUUSD+** 之間的價差，並在符合條件時自動執行套利交易。

## 🎯 套利策略

### 進場條件
- 價差大於 **0.4%**
- XAUTUSDT 資金費率為 **負值**

### 交易邏輯
- 價高的平台做空，價低的平台做多
- 兩邊使用相同的持倉價值（10倍槓桿）
- 通常 MT5 價格較高，所以多數情況是賣出 MT5，買入 Bybit

### 平倉條件
- 價差小於 **0.2%** 時自動平倉

## 🏗️ 系統架構

### 核心組件
- **`xau_arbitrage_trader.py`** - 主要套利交易系統
- **`bybit_futures_client.py`** - Bybit 永續合約 API 客戶端
- **`bybit_mt5_client.py`** - Bybit MT5 平台客戶端
- **`telegram_notifier.py`** - Telegram 通知模組
- **`xau_spread_monitor.py`** - 價差監控系統（原有）

### 輔助工具
- **`test_arbitrage_system.py`** - 系統測試腳本
- **`debug_mt5_symbols.py`** - MT5 交易對除錯工具

## 🚀 快速開始

### 1. 環境設定

```bash
# 安裝依賴套件
pip install -r requirements.txt
```

### 2. 配置檔案

編輯 `config.env` 檔案：

```env
# Bybit API 設定
BYBIT_API_KEY=您的Bybit_API金鑰
BYBIT_API_SECRET=您的Bybit_API密鑰

# Bybit MT5 設定
BYBIT_MT5_LOGIN=您的MT5帳號
BYBIT_MT5_PASSWORD=您的MT5密碼
BYBIT_MT5_SERVER=Bybit-Live-2
BYBIT_MT5_PATH=C:\\Program Files\\MetaTrader 5\\terminal64.exe

# Telegram 通知設定
TELEGRAM_BOT_TOKEN=您的Telegram_Bot_Token
TELEGRAM_CHAT_ID=您的Telegram_Chat_ID
```

### 3. 測試系統

```bash
# 運行完整測試
python test_arbitrage_system.py
```

### 4. 啟動套利系統

```bash
# 啟動自動套利交易
python xau_arbitrage_trader.py
```

## 📊 功能特色

### 🔍 即時監控
- 每分鐘檢查價差和資金費率
- 自動計算套利機會
- 即時價格更新

### 🤖 自動交易
- 符合條件時自動開倉
- 達到平倉條件時自動平倉
- 市價單執行，確保快速成交

### 📱 Telegram 通知
- 套利機會偵測通知
- 交易執行通知
- 平倉結果通知
- 錯誤警報通知

### 💾 數據記錄
- 自動保存交易歷史
- 價差數據記錄
- 收益統計分析

## 🔧 系統設定

### 交易參數
- **進場價差閾值**: 0.4%
- **平倉價差閾值**: 0.2%
- **槓桿倍數**: 10x
- **資金使用比例**: 10%

### 監控設定
- **檢查間隔**: 60秒
- **價格來源**: Bybit API + MT5 即時報價
- **資金費率檢查**: 每8小時更新

## 📈 風險管理

### 資金管理
- 每次交易使用帳戶餘額的 10%
- 兩邊持倉價值相等，降低風險
- 自動計算保證金需求

### 止損機制
- 價差收斂時自動平倉
- 系統錯誤時停止交易
- 網路異常時重試機制

## 🚀 Railway 部署

### 1. 準備部署
```bash
# 確保所有檔案已準備就緒
git add .
git commit -m "準備 Railway 部署"
```

### 2. 部署到 Railway
- 將專案推送到 GitHub
- 在 Railway 中連接 GitHub 倉庫
- 設定環境變數（從 config.env 複製）
- 部署並啟動服務

### 3. 環境變數設定
在 Railway 中設定以下環境變數：
- `BYBIT_API_KEY`
- `BYBIT_API_SECRET`
- `BYBIT_MT5_LOGIN`
- `BYBIT_MT5_PASSWORD`
- `BYBIT_MT5_SERVER`
- `TELEGRAM_BOT_TOKEN`
- `TELEGRAM_CHAT_ID`

## 📝 注意事項

### ⚠️ 重要提醒
1. **這是 LIVE 交易系統**，請謹慎使用
2. 建議先在測試環境中驗證
3. 請妥善保管 API 金鑰和密碼
4. 定期檢查系統運行狀態

### 🔒 安全建議
- 使用專用的交易帳戶
- 設定適當的 API 權限
- 定期更換 API 金鑰
- 監控異常交易活動

## 📞 技術支援

如遇問題，請檢查：
1. API 金鑰是否正確
2. 網路連接是否正常
3. MT5 平台是否可連接
4. Telegram Bot 是否設定正確

## 📄 授權

本專案僅供學習和研究使用，使用者需自行承擔交易風險。
