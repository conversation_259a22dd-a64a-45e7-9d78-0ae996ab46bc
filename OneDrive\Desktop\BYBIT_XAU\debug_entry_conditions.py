"""
調試進場條件 - 檢查實際有多少個數據點符合-0.85%條件
"""
import pandas as pd
import numpy as np

def debug_entry_conditions():
    """調試進場條件"""
    
    # 載入數據
    try:
        data = pd.read_csv('data/xau_spread_analysis_simple.csv')
        print(f"✅ 成功載入 {len(data)} 筆數據")
    except FileNotFoundError:
        print("❌ 找不到數據文件")
        return
    
    # 檢查-0.85%條件
    entry_threshold = -0.85
    
    # 符合進場條件的數據點
    entry_points = data[data['price_diff_pct'] <= entry_threshold]
    
    print(f"\n📊 進場條件分析 (差價 <= {entry_threshold}%):")
    print(f"   符合條件的數據點: {len(entry_points)} 個")
    print(f"   佔總數據比例: {len(entry_points)/len(data)*100:.2f}%")
    print(f"   平均每天機會: {len(entry_points)/7:.1f} 次")
    
    if len(entry_points) > 0:
        print(f"\n📋 符合條件的時間點:")
        for i, row in entry_points.iterrows():
            print(f"   {row['time']} | 差價: {row['price_diff_pct']:.4f}%")
    
    # 檢查不同閾值的機會數量
    print(f"\n📈 不同閾值的機會統計:")
    thresholds = [-0.8, -0.82, -0.84, -0.85, -0.86, -0.88, -0.9]
    for threshold in thresholds:
        count = len(data[data['price_diff_pct'] <= threshold])
        daily_avg = count / 7
        print(f"   <= {threshold:5.2f}%: {count:3d}次 ({count/len(data)*100:5.2f}%) - 每天{daily_avg:4.1f}次")
    
    # 檢查數據的時間範圍
    data['time'] = pd.to_datetime(data['time'])
    print(f"\n📅 數據時間範圍:")
    print(f"   開始時間: {data['time'].min()}")
    print(f"   結束時間: {data['time'].max()}")
    print(f"   總天數: {(data['time'].max() - data['time'].min()).days + 1} 天")
    
    # 檢查是否有連續的進場條件（這可能導致只記錄一次交易）
    if len(entry_points) > 0:
        entry_points = entry_points.sort_values('time')
        print(f"\n⏰ 進場時間間隔分析:")
        for i in range(1, min(len(entry_points), 10)):  # 只看前10個
            time_diff = (entry_points.iloc[i]['time'] - entry_points.iloc[i-1]['time'])
            if hasattr(time_diff, 'total_seconds'):
                minutes = time_diff.total_seconds() / 60
            else:
                # 如果是字符串，先轉換
                time1 = pd.to_datetime(entry_points.iloc[i]['time'])
                time2 = pd.to_datetime(entry_points.iloc[i-1]['time'])
                minutes = (time1 - time2).total_seconds() / 60
            print(f"   間隔 {i}: {minutes:.0f} 分鐘")

def main():
    debug_entry_conditions()

if __name__ == "__main__":
    main()
