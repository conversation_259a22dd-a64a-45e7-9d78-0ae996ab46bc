#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的 Telegram 測試腳本
"""

import os
import requests
from dotenv import load_dotenv

load_dotenv('config.env')

def test_telegram_simple():
    """簡單的 Telegram 測試"""
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')
    
    print(f"Bot Token: {bot_token[:20]}..." if bot_token else "Bot Token: None")
    print(f"Chat ID: {chat_id}")
    
    if not bot_token or not chat_id:
        print("❌ Bot Token 或 Chat ID 未設定")
        return False
    
    # 測試 1: 簡單文字訊息
    print("\n📤 測試 1: 簡單文字訊息")
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    data = {
        "chat_id": chat_id,
        "text": "Hello from XAU Arbitrage System!",
        "parse_mode": "HTML"
    }
    
    try:
        response = requests.post(url, data=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ 簡單訊息發送成功")
            return True
        else:
            print("❌ 簡單訊息發送失敗")
            return False
            
    except Exception as e:
        print(f"❌ 請求錯誤: {e}")
        return False

def test_telegram_get_me():
    """測試 Bot 資訊"""
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    
    print("\n🤖 測試 Bot 資訊")
    url = f"https://api.telegram.org/bot{bot_token}/getMe"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Bot 資訊獲取成功")
            return True
        else:
            print("❌ Bot 資訊獲取失敗")
            return False
            
    except Exception as e:
        print(f"❌ 請求錯誤: {e}")
        return False

def test_telegram_get_updates():
    """測試 Bot 更新"""
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    
    print("\n📬 測試 Bot 更新")
    url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Bot 更新獲取成功")
            return True
        else:
            print("❌ Bot 更新獲取失敗")
            return False
            
    except Exception as e:
        print(f"❌ 請求錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Telegram 診斷測試")
    print("=" * 50)
    
    # 測試 Bot 資訊
    test_telegram_get_me()
    
    # 測試 Bot 更新
    test_telegram_get_updates()
    
    # 測試發送訊息
    test_telegram_simple() 