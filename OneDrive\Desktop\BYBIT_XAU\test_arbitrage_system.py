#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
套利系統測試腳本
---------------
測試各個組件的功能
"""

import os
import time
from datetime import datetime
from dotenv import load_dotenv

from bybit_futures_client import BybitFuturesClient
from bybit_mt5_client import BybitMT5Client
from telegram_notifier import TelegramNotifier
from xau_arbitrage_trader import XAUArbitrageTrader

load_dotenv('config.env')

def test_bybit_futures():
    """測試 Bybit 永續合約客戶端"""
    print("=" * 50)
    print("🔍 測試 Bybit 永續合約客戶端")
    print("=" * 50)
    
    client = BybitFuturesClient()
    success = client.test_connection()
    
    if success:
        print("✅ Bybit 永續合約客戶端測試通過")
    else:
        print("❌ Bybit 永續合約客戶端測試失敗")
    
    return success

def test_mt5_client():
    """測試 MT5 客戶端"""
    print("=" * 50)
    print("🔍 測試 Bybit MT5 客戶端")
    print("=" * 50)
    
    client = BybitMT5Client()
    success = client.test_connection()
    
    if success:
        print("✅ Bybit MT5 客戶端測試通過")
    else:
        print("❌ Bybit MT5 客戶端測試失敗")
    
    return success

def test_telegram_notifier():
    """測試 Telegram 通知"""
    print("=" * 50)
    print("🔍 測試 Telegram 通知")
    print("=" * 50)
    
    notifier = TelegramNotifier()
    success = notifier.test_connection()
    
    if success:
        print("✅ Telegram 通知測試通過")
    else:
        print("❌ Telegram 通知測試失敗")
    
    return success

def test_price_monitoring():
    """測試價格監控"""
    print("=" * 50)
    print("🔍 測試價格監控")
    print("=" * 50)
    
    trader = XAUArbitrageTrader()
    
    # 獲取當前價格
    prices = trader.get_current_prices()
    if len(prices) >= 2:
        print(f"✅ 價格獲取成功")
        print(f"   Bybit XAUTUSDT: {prices['bybit']:.2f}")
        print(f"   MT5 XAUUSD+: {prices['mt5']:.2f}")
        
        # 計算價差
        spread_info = trader.calculate_spread(prices['bybit'], prices['mt5'])
        print(f"   絕對價差: {spread_info['absolute']:.2f}")
        print(f"   百分比價差: {spread_info['percentage']:.3f}%")
        print(f"   方向: {spread_info['direction']}")
        
        # 獲取資金費率
        funding_rate = trader.bybit_client.get_funding_rate("XAUTUSDT")
        if funding_rate is not None:
            print(f"   資金費率: {funding_rate:.6f}")
        
        return True
    else:
        print("❌ 價格獲取失敗")
        return False

def test_arbitrage_conditions():
    """測試套利條件檢查"""
    print("=" * 50)
    print("🔍 測試套利條件檢查")
    print("=" * 50)
    
    trader = XAUArbitrageTrader()
    
    # 模擬不同的價差和資金費率情況
    test_cases = [
        {"spread": 0.5, "funding_rate": -0.0001, "expected": True},
        {"spread": 0.3, "funding_rate": -0.0001, "expected": False},
        {"spread": 0.5, "funding_rate": 0.0001, "expected": False},
        {"spread": -0.5, "funding_rate": -0.0001, "expected": True},
    ]
    
    for i, case in enumerate(test_cases, 1):
        spread_info = {"percentage": case["spread"]}
        funding_rate = case["funding_rate"]
        expected = case["expected"]
        
        result = trader.check_arbitrage_conditions(spread_info, funding_rate)
        
        if result == expected:
            print(f"✅ 測試案例 {i}: 通過")
        else:
            print(f"❌ 測試案例 {i}: 失敗 (期望: {expected}, 實際: {result})")
    
    return True

def test_position_calculation():
    """測試持倉大小計算"""
    print("=" * 50)
    print("🔍 測試持倉大小計算")
    print("=" * 50)
    
    trader = XAUArbitrageTrader()
    
    # 模擬價格
    bybit_price = 3300.0
    mt5_price = 3310.0
    
    position_sizes = trader.calculate_position_sizes(bybit_price, mt5_price)
    
    if position_sizes:
        print(f"✅ 持倉大小計算成功")
        print(f"   Bybit 數量: {position_sizes['bybit_qty']:.3f}")
        print(f"   MT5 數量: {position_sizes['mt5_qty']:.2f}")
        print(f"   持倉價值: {position_sizes['position_value']:.2f}")
        return True
    else:
        print("❌ 持倉大小計算失敗")
        return False

def test_telegram_notifications():
    """測試 Telegram 通知功能"""
    print("=" * 50)
    print("🔍 測試 Telegram 通知功能")
    print("=" * 50)
    
    notifier = TelegramNotifier()
    
    # 測試套利機會通知
    print("📤 發送套利機會通知...")
    success1 = notifier.send_arbitrage_opportunity(
        bybit_price=3300.0,
        mt5_price=3315.0,
        spread=15.0,
        percentage_spread=0.45,
        funding_rate=-0.0001
    )
    
    # 測試交易執行通知
    print("📤 發送交易執行通知...")
    trade_info = {
        'trade_id': 'TEST123',
        'bybit_side': 'Buy',
        'mt5_side': 'Sell',
        'bybit_qty': 0.1,
        'mt5_qty': 0.1,
        'bybit_price': 3300.0,
        'mt5_price': 3315.0,
        'bybit_margin': 330.0,
        'mt5_margin': 331.5,
        'bybit_fee': 0.33,
        'mt5_fee': 0.33,
        'total_margin': 661.5,
        'expected_profit': 15.0
    }
    success2 = notifier.send_trade_execution(trade_info)
    
    # 測試平倉通知
    print("📤 發送平倉通知...")
    close_info = {
        'trade_id': 'TEST123',
        'bybit_close_price': 3305.0,
        'mt5_close_price': 3310.0,
        'bybit_pnl': 5.0,
        'mt5_pnl': -5.0,
        'total_pnl': 0.0,
        'roi': 0.0,
        'duration': '00:05:30'
    }
    success3 = notifier.send_trade_close(close_info)
    
    if success1 and success2 and success3:
        print("✅ Telegram 通知測試通過")
        return True
    else:
        print("❌ Telegram 通知測試失敗")
        return False

def run_all_tests():
    """運行所有測試"""
    print("🚀 開始套利系統測試")
    print("=" * 60)
    
    test_results = []
    
    # 測試各個組件
    test_results.append(("Bybit 永續合約", test_bybit_futures()))
    test_results.append(("Bybit MT5", test_mt5_client()))
    test_results.append(("Telegram 通知", test_telegram_notifier()))
    test_results.append(("價格監控", test_price_monitoring()))
    test_results.append(("套利條件", test_arbitrage_conditions()))
    test_results.append(("持倉計算", test_position_calculation()))
    test_results.append(("通知功能", test_telegram_notifications()))
    
    # 總結測試結果
    print("=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:15s}: {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！系統準備就緒")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關設定")
        return False

if __name__ == "__main__":
    run_all_tests() 