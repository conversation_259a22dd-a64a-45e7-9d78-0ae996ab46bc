"""
第一階段：BYBIT XAUTUSDT vs BYBIT MT5 XAUUSD+ 簡化歷史數據分析
獲取近7個交易日的每分鐘收盤價，計算差價分布
"""
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
from dotenv import load_dotenv
import requests
import MetaTrader5 as mt5
import time

# 載入環境變數
load_dotenv('config.env')

class SimpleXAUAnalysis:
    def __init__(self):
        """初始化簡化分析系統"""
        self.bybit_api_key = os.getenv('BYBIT_API_KEY')
        self.bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        # MT5 設定
        self.mt5_login = int(os.getenv('BYBIT_MT5_LOGIN'))
        self.mt5_password = os.getenv('BYBIT_MT5_PASSWORD')
        self.mt5_server = os.getenv('BYBIT_MT5_SERVER')
        self.mt5_path = os.getenv('BYBIT_MT5_PATH')
        
        # 時區設定
        self.timezone = pytz.timezone('Asia/Taipei')
        
        print("XAU套利簡化分析系統初始化完成")
    
    def get_bybit_7days_data(self, end_time):
        """獲取Bybit XAUTUSDT 7個交易日的歷史K線數據"""
        print(f"正在獲取Bybit XAUTUSDT 7個交易日的1分鐘K線數據...")

        url = "https://api.bybit.com/v5/market/kline"
        all_data = []

        # 7個交易日大約需要 7 * 24 * 60 = 10080 根K線
        # 但考慮到週末和節假日，我們分批獲取更多數據
        total_needed = 12000  # 多獲取一些確保有7個交易日
        current_end = int(end_time.timestamp() * 1000)

        while len(all_data) < total_needed:
            params = {
                'category': 'linear',
                'symbol': 'XAUTUSDT',
                'interval': '1',  # 1分鐘
                'limit': 1000,  # API限制每次最多1000根
                'end': current_end
            }
        
            try:
                response = requests.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if data['retCode'] == 0 and data['result']['list']:
                    klines = data['result']['list']
                    all_data.extend(klines)

                    print(f"已獲取 {len(klines)} 根K線，總計 {len(all_data)} 根")

                    # 更新下一批的結束時間 (使用最早的時間戳)
                    if klines:
                        current_end = int(klines[-1][0]) - 1  # 減1毫秒避免重複

                    # 如果返回的數據少於1000根，說明已經到達數據邊界
                    if len(klines) < 1000:
                        print("已獲取所有可用的歷史數據")
                        break

                    # 添加延遲避免API限制
                    time.sleep(0.1)
                else:
                    print(f"API返回錯誤: {data}")
                    break

            except Exception as e:
                print(f"獲取Bybit數據時發生錯誤: {e}")
                break

        if all_data:
            # 轉換為DataFrame
            df = pd.DataFrame(all_data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
            ])

            # 轉換數據類型
            df['timestamp'] = pd.to_datetime(df['timestamp'].astype(float), unit='ms')
            df['close'] = df['close'].astype(float)
            df = df.sort_values('timestamp').reset_index(drop=True)

            # 設定時區
            df['timestamp'] = df['timestamp'].dt.tz_localize('UTC').dt.tz_convert(self.timezone)

            bybit_data = df[['timestamp', 'close']].copy()
            bybit_data.columns = ['time', 'bybit_price']

            print(f"Bybit數據獲取完成: {len(bybit_data)} 筆記錄")
            print(f"時間範圍: {bybit_data['time'].min()} 到 {bybit_data['time'].max()}")
            return bybit_data
        else:
            print("未能獲取任何Bybit數據")
            return None
    
    def get_mt5_7days_data(self, days=10):
        """獲取MT5 XAUUSD+ 7個交易日的數據"""
        print(f"正在獲取MT5 XAUUSD+ 近{days}天的1分鐘數據 (確保包含7個交易日)...")

        # 初始化MT5
        if not mt5.initialize(path=self.mt5_path):
            print(f"MT5初始化失敗: {mt5.last_error()}")
            return None

        # 登入MT5
        if not mt5.login(self.mt5_login, password=self.mt5_password, server=self.mt5_server):
            print(f"MT5登入失敗: {mt5.last_error()}")
            mt5.shutdown()
            return None

        print("MT5連接成功")

        # 獲取更大範圍的數據確保包含7個完整交易日
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)  # 獲取10天數據確保有7個交易日

        # 獲取1分鐘K線數據
        rates = mt5.copy_rates_range("XAUUSD+", mt5.TIMEFRAME_M1, start_time, end_time)
        
        mt5.shutdown()
        
        if rates is not None and len(rates) > 0:
            # 轉換為DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            # 設定時區
            df['time'] = df['time'].dt.tz_localize('UTC').dt.tz_convert(self.timezone)
            
            mt5_data = df[['time', 'close']].copy()
            mt5_data.columns = ['time', 'mt5_price']
            
            print(f"MT5數據獲取完成: {len(mt5_data)} 筆記錄")
            print(f"時間範圍: {mt5_data['time'].min()} 到 {mt5_data['time'].max()}")
            return mt5_data
        else:
            print("未能獲取MT5數據")
            return None
    
    def analyze_spreads(self, bybit_data, mt5_data):
        """分析差價"""
        if bybit_data is None or mt5_data is None:
            print("缺少必要的價格數據")
            return None
        
        print("正在合併數據並計算差價...")
        
        # 合併數據 (使用時間戳對齊)
        merged = pd.merge_asof(
            bybit_data.sort_values('time'),
            mt5_data.sort_values('time'),
            on='time',
            direction='nearest',
            tolerance=pd.Timedelta('10 minutes')  # 允許10分鐘的時間差
        )

        print(f"合併前 - Bybit數據: {len(bybit_data)} 筆")
        print(f"合併前 - MT5數據: {len(mt5_data)} 筆")
        print(f"合併後 - 總數據: {len(merged)} 筆")
        
        # 檢查合併結果
        print(f"合併後有MT5價格的記錄: {len(merged[merged['mt5_price'].notna()])} 筆")

        # 移除缺失數據
        merged = merged.dropna()

        if len(merged) == 0:
            print("合併後沒有有效數據")
            print("調試信息:")
            print(f"Bybit時間範圍: {bybit_data['time'].min()} 到 {bybit_data['time'].max()}")
            print(f"MT5時間範圍: {mt5_data['time'].min()} 到 {mt5_data['time'].max()}")

            # 嘗試找到最接近的時間
            bybit_latest = bybit_data['time'].max()
            mt5_latest = mt5_data['time'].max()
            print(f"時間差: {abs(bybit_latest - mt5_latest)}")

            return None
        
        # 計算差價
        merged['price_diff'] = merged['bybit_price'] - merged['mt5_price']
        merged['price_diff_pct'] = (merged['price_diff'] / merged['mt5_price']) * 100
        
        # 添加日期列
        merged['date'] = merged['time'].dt.date
        
        print(f"數據合併完成: {len(merged)} 筆有效記錄")
        
        # 分析差價分布
        self.print_analysis_results(merged)
        
        # 保存數據
        self.save_results(merged)
        
        return merged
    
    def print_analysis_results(self, data):
        """打印分析結果"""
        print("="*80)
        print("BYBIT XAUTUSDT vs BYBIT MT5 XAUUSD+ 差價分析報告")
        print("="*80)
        
        # 基本統計
        spread_pct = data['price_diff_pct']
        
        print(f"\n📊 基本統計:")
        print(f"數據筆數: {len(data):,}")
        print(f"時間範圍: {data['time'].min()} 到 {data['time'].max()}")
        print(f"最大正差價: {spread_pct.max():.6f}%")
        print(f"最小負差價: {spread_pct.min():.6f}%")
        print(f"平均差價: {spread_pct.mean():.6f}%")
        print(f"標準差: {spread_pct.std():.6f}%")
        print(f"中位數: {spread_pct.median():.6f}%")
        
        # 正負差價分布
        positive_spreads = spread_pct[spread_pct > 0]
        negative_spreads = spread_pct[spread_pct < 0]
        zero_spreads = spread_pct[spread_pct == 0]
        
        print(f"\n📈 差價方向分布:")
        print(f"正差價 (Bybit > MT5): {len(positive_spreads):,} 次 ({len(positive_spreads)/len(spread_pct)*100:.2f}%)")
        print(f"負差價 (Bybit < MT5): {len(negative_spreads):,} 次 ({len(negative_spreads)/len(spread_pct)*100:.2f}%)")
        print(f"零差價: {len(zero_spreads):,} 次 ({len(zero_spreads)/len(spread_pct)*100:.2f}%)")
        
        # 差價閾值分析
        print(f"\n🎯 不同閾值下的套利機會:")
        print("-"*80)
        print(f"{'差價閾值':<12} {'正差價機會':<10} {'負差價機會':<10} {'總機會':<8} {'佔比':<8}")
        print("-"*80)
        
        thresholds = [0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5]
        
        for threshold in thresholds:
            pos_count = len(positive_spreads[positive_spreads >= threshold])
            neg_count = len(negative_spreads[negative_spreads <= -threshold])
            total_count = pos_count + neg_count
            percentage = total_count / len(spread_pct) * 100
            
            print(f"> {threshold:.3f}%{'':<5} {pos_count:<10} {neg_count:<10} {total_count:<8} {percentage:>6.2f}%")
        
        # 百分位數分析
        print(f"\n📊 百分位數分析:")
        print("-"*50)
        
        percentiles = [99.9, 99.5, 99, 95, 90, 75, 50, 25, 10, 5, 1, 0.5, 0.1]
        
        for p in percentiles:
            value = np.percentile(spread_pct, p)
            print(f"{p:5.1f}百分位: {value:8.6f}%")
    
    def save_results(self, data):
        """保存分析結果"""
        os.makedirs('data', exist_ok=True)
        
        # 保存原始數據
        data.to_csv('data/xau_spread_analysis_simple.csv', index=False)
        print(f"\n✅ 差價數據已保存到: data/xau_spread_analysis_simple.csv")
        
        # 保存統計摘要
        spread_pct = data['price_diff_pct']
        
        summary = {
            'total_records': len(data),
            'max_positive_spread': spread_pct.max(),
            'min_negative_spread': spread_pct.min(),
            'mean_spread': spread_pct.mean(),
            'std_spread': spread_pct.std(),
            'median_spread': spread_pct.median(),
            'positive_count': len(spread_pct[spread_pct > 0]),
            'negative_count': len(spread_pct[spread_pct < 0]),
            'zero_count': len(spread_pct[spread_pct == 0])
        }
        
        summary_df = pd.DataFrame([summary])
        summary_df.to_csv('data/xau_spread_summary_simple.csv', index=False)
        print(f"✅ 統計摘要已保存到: data/xau_spread_summary_simple.csv")
    
    def run_analysis(self):
        """執行完整的7個交易日分析"""
        print("開始執行XAU套利7個交易日分析...")

        # 步驟1: 獲取MT5數據 (先獲取MT5數據確定時間範圍)
        mt5_data = self.get_mt5_7days_data(days=10)
        if mt5_data is None:
            print("❌ MT5數據獲取失敗")
            return False

        # 使用MT5數據的最新時間作為Bybit數據的結束時間
        mt5_latest_time = mt5_data['time'].max()
        print(f"使用MT5最新時間作為參考: {mt5_latest_time}")

        # 步驟2: 獲取Bybit 7個交易日的歷史數據
        bybit_data = self.get_bybit_7days_data(
            end_time=mt5_latest_time.to_pydatetime().replace(tzinfo=None)
        )
        if bybit_data is None:
            print("❌ Bybit數據獲取失敗")
            return False
        
        # 步驟3: 分析差價
        result = self.analyze_spreads(bybit_data, mt5_data)
        
        if result is not None:
            print("\n🎉 簡化分析完成！")
            return True
        else:
            print("\n❌ 分析失敗")
            return False

def main():
    """主函數"""
    analyzer = SimpleXAUAnalysis()
    
    # 執行簡化分析
    success = analyzer.run_analysis()
    
    if success:
        print("\n✅ 第一階段歷史數據分析完成")
        print("📁 結果文件:")
        print("   - data/xau_spread_analysis_simple.csv (詳細數據)")
        print("   - data/xau_spread_summary_simple.csv (統計摘要)")
    else:
        print("\n❌ 分析失敗，請檢查配置和網絡連接")

if __name__ == "__main__":
    main()
