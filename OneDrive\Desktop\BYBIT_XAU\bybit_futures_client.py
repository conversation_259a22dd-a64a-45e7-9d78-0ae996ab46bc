#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bybit 永續合約交易客戶端
------------------------
處理 Bybit 永續合約的下單、平倉、查詢等功能
"""

import os
import time
import hmac
import hashlib
import requests
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from urllib.parse import urlencode

load_dotenv('config.env')

class BybitFuturesClient:
    def __init__(self):
        self.api_key = os.getenv('BYBIT_API_KEY')
        self.api_secret = os.getenv('BYBIT_API_SECRET')
        self.base_url = "https://api.bybit.com"
        self.session = requests.Session()
        
    def _generate_signature(self, timestamp: str, recv_window: str, params: str) -> str:
        """生成 API 簽名"""
        param_str = str(timestamp) + self.api_key + recv_window + params
        hash = hmac.new(
            bytes(self.api_secret, "utf-8"),
            param_str.encode("utf-8"),
            hashlib.sha256
        )
        return hash.hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False) -> Dict:
        """發送 API 請求"""
        url = self.base_url + endpoint
        
        if params is None:
            params = {}
        
        if signed:
            timestamp = str(int(time.time() * 1000))
            recv_window = "5000"
            param_str = urlencode(params)
            signature = self._generate_signature(timestamp, recv_window, param_str)
            
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": timestamp,
                "X-BAPI-RECV-WINDOW": recv_window,
                "Content-Type": "application/json"
            }
        else:
            headers = {"Content-Type": "application/json"}
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=params, headers=headers, timeout=10)
            else:
                response = self.session.post(url, json=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API 請求失敗: {response.status_code} - {response.text}")
                return {"retCode": -1, "retMsg": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"請求錯誤: {e}")
            return {"retCode": -1, "retMsg": str(e)}
    
    def get_account_info(self) -> Optional[Dict]:
        """獲取帳戶資訊"""
        endpoint = "/v5/account/wallet-balance"
        params = {"accountType": "UNIFIED"}
        
        result = self._make_request("GET", endpoint, params, signed=True)
        
        if result.get("retCode") == 0:
            return result.get("result", {})
        else:
            print(f"獲取帳戶資訊失敗: {result}")
            return None
    
    def get_position_info(self, symbol: str = "XAUTUSDT") -> Optional[Dict]:
        """獲取持倉資訊"""
        endpoint = "/v5/position/list"
        params = {
            "category": "linear",
            "symbol": symbol
        }
        
        result = self._make_request("GET", endpoint, params, signed=True)
        
        if result.get("retCode") == 0:
            positions = result.get("result", {}).get("list", [])
            for position in positions:
                if position.get("symbol") == symbol:
                    return position
            return None
        else:
            print(f"獲取持倉資訊失敗: {result}")
            return None
    
    def get_funding_rate(self, symbol: str = "XAUTUSDT") -> Optional[float]:
        """獲取資金費率"""
        endpoint = "/v5/market/funding/history"
        params = {
            "category": "linear",
            "symbol": symbol,
            "limit": 1
        }
        
        result = self._make_request("GET", endpoint, params, signed=False)
        
        if result.get("retCode") == 0:
            funding_list = result.get("result", {}).get("list", [])
            if funding_list:
                return float(funding_list[0].get("fundingRate", 0))
        return None
    
    def place_order(self, symbol: str, side: str, qty: float, order_type: str = "Market") -> Optional[Dict]:
        """下單"""
        endpoint = "/v5/order/create"
        params = {
            "category": "linear",
            "symbol": symbol,
            "side": side,  # "Buy" or "Sell"
            "orderType": order_type,
            "qty": str(qty),
            "timeInForce": "GTC"
        }
        
        result = self._make_request("POST", endpoint, params, signed=True)
        
        if result.get("retCode") == 0:
            return result.get("result", {})
        else:
            print(f"下單失敗: {result}")
            return None
    
    def close_position(self, symbol: str, side: str, qty: float) -> Optional[Dict]:
        """平倉"""
        return self.place_order(symbol, side, qty, "Market")
    
    def get_order_history(self, symbol: str = "XAUTUSDT", limit: int = 10) -> List[Dict]:
        """獲取訂單歷史"""
        endpoint = "/v5/order/history"
        params = {
            "category": "linear",
            "symbol": symbol,
            "limit": limit
        }
        
        result = self._make_request("GET", endpoint, params, signed=True)
        
        if result.get("retCode") == 0:
            return result.get("result", {}).get("list", [])
        else:
            print(f"獲取訂單歷史失敗: {result}")
            return []
    
    def get_ticker_price(self, symbol: str = "XAUTUSDT") -> Optional[float]:
        """獲取最新價格"""
        endpoint = "/v5/market/tickers"
        params = {
            "category": "linear",
            "symbol": symbol
        }
        
        result = self._make_request("GET", endpoint, params, signed=False)
        
        if result.get("retCode") == 0:
            ticker_list = result.get("result", {}).get("list", [])
            if ticker_list:
                return float(ticker_list[0].get("lastPrice", 0))
        return None
    
    def calculate_position_size(self, account_balance: float, leverage: int = 10, price: float = None) -> float:
        """計算持倉大小（以 USDT 為單位）"""
        if price is None:
            price = self.get_ticker_price("XAUTUSDT")
            if price is None:
                return 0
        
        # 使用 10% 的帳戶餘額
        available_balance = account_balance * 0.1
        position_value = available_balance * leverage
        position_size = position_value / price
        
        return round(position_size, 3)
    
    def test_connection(self) -> bool:
        """測試連接"""
        print("🔍 測試 Bybit 永續合約連接...")
        
        # 測試帳戶資訊
        account_info = self.get_account_info()
        if account_info:
            print("✅ 帳戶資訊獲取成功")
            wallet_list = account_info.get("list", [])
            if wallet_list:
                wallet = wallet_list[0]
                total_balance = float(wallet.get("totalWalletBalance", 0))
                available_balance = float(wallet.get("availableToWithdraw", 0))
                print(f"   總餘額: {total_balance:.2f} USDT")
                print(f"   可用餘額: {available_balance:.2f} USDT")
        
        # 測試價格獲取
        price = self.get_ticker_price("XAUTUSDT")
        if price:
            print(f"✅ XAUTUSDT 價格獲取成功: {price:.2f}")
        
        # 測試資金費率
        funding_rate = self.get_funding_rate("XAUTUSDT")
        if funding_rate is not None:
            print(f"✅ 資金費率獲取成功: {funding_rate:.6f}")
        
        # 測試持倉資訊
        position = self.get_position_info("XAUTUSDT")
        if position:
            print("✅ 持倉資訊獲取成功")
            size = float(position.get("size", 0))
            side = position.get("side", "")
            print(f"   持倉大小: {size}")
            print(f"   持倉方向: {side}")
        else:
            print("ℹ️ 目前無 XAUTUSDT 持倉")
        
        return True

# 測試函數
if __name__ == "__main__":
    client = BybitFuturesClient()
    client.test_connection() 