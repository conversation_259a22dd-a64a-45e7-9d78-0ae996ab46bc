# Bybit MT5 設置指南

## 第一步：下載並安裝 MetaTrader 5

1. **下載 MT5**：
   - 前往 Bybit 官網：https://www.bybit.com/
   - 登入您的帳戶
   - 找到 "Trading" → "MetaTrader 5"
   - 下載 MT5 終端

2. **安裝 MT5**：
   - 執行下載的安裝檔
   - 選擇安裝路徑（預設：`C:\Program Files\MetaTrader 5\`）
   - 完成安裝

## 第二步：配置 MT5 連接

1. **啟動 MT5**：
   - 開啟 MetaTrader 5 終端
   - 在登入視窗中選擇 "Bybit-Live" 伺服器

2. **登入設定**：
   - 伺服器：`Bybit-Live`
   - 帳號：您的 Bybit MT5 帳號
   - 密碼：您的 Bybit MT5 密碼
   - 點擊 "OK" 登入

3. **驗證連接**：
   - 登入成功後，您應該能看到 Market Watch 視窗
   - 確認可以看到外匯交易對（如 USDJPY+）

## 第三步：配置 Python 環境

1. **確認 Python 套件已安裝**：
   ```bash
   pip install MetaTrader5
   ```

2. **更新 config.env 檔案**：
   在 `config.env` 中添加以下設定：
   ```env
   # Bybit MT5 設定
   BYBIT_MT5_LOGIN=您的MT5帳號
   BYBIT_MT5_PASSWORD=您的MT5密碼
   BYBIT_MT5_SERVER=Bybit-Live
   BYBIT_MT5_PATH=C:\\Program Files\\MetaTrader 5\\terminal64.exe
   ```

## 第四步：測試連接

1. **運行測試腳本**：
   ```bash
   python bybit_mt5_client.py
   ```

2. **預期結果**：
   - ✅ Bybit MT5 連接成功
   - ✅ 可用交易對數量顯示
   - ✅ USDJPY 報價測試成功

## 常見問題解決

### 問題 1：MT5 初始化失敗
**解決方案**：
- 確認 MT5 已正確安裝
- 檢查 `BYBIT_MT5_PATH` 路徑是否正確
- 確保 MT5 終端已關閉（避免衝突）

### 問題 2：登入失敗
**解決方案**：
- 確認帳號密碼正確
- 確認伺服器名稱正確（Bybit-Live）
- 檢查網路連接

### 問題 3：無法獲取 USDJPY 報價
**解決方案**：
- 確認 USDJPY+ 在 Market Watch 中可見
- 嘗試其他代號：USDJPY, USDJPY.m
- 檢查帳戶權限

### 問題 4：Python 整合失敗
**解決方案**：
- 確認 MetaTrader5 Python 套件已安裝
- 重新啟動 Python 環境
- 檢查 Python 版本相容性

## 重要提醒

1. **安全性**：
   - 請妥善保管您的 MT5 帳號密碼
   - 不要在公開場合分享連接資訊

2. **交易風險**：
   - 這是 LIVE 帳戶，請謹慎操作
   - 建議先在小額測試環境中驗證

3. **技術支援**：
   - 如遇問題，請聯繫 Bybit 客服
   - 或查看 Bybit MT5 官方文檔

## 下一步

設置完成後，我們將：
1. 建立 FXCM vs Bybit MT5 的價差監控系統
2. 實現 USDJPY 套利策略
3. 自動化交易執行

準備好後請告訴我，我們開始實作套利系統！ 