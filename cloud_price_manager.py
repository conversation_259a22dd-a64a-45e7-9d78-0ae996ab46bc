#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端价格管理器
提供多重备用价格源，确保云端环境的价格获取稳定性
"""

import requests
import time
import logging
from typing import Optional, Dict, List
from datetime import datetime, timedelta

class CloudPriceManager:
    """云端价格管理器 - 多重备用价格源"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.price_cache = {}
        self.cache_duration = 30  # 缓存30秒
        
        # 价格源配置（按优先级排序）
        self.price_sources = [
            {
                'name': 'Bybit现货XAUUSDT',
                'method': self._get_bybit_spot_price,
                'weight': 1.0,  # 权重最高，最接近MT5
                'timeout': 10
            },
            {
                'name': 'Yahoo Finance黄金期货',
                'method': self._get_yahoo_gold_price,
                'weight': 0.95,
                'timeout': 10
            },
            {
                'name': 'CoinGecko黄金价格',
                'method': self._get_coingecko_price,
                'weight': 0.90,
                'timeout': 10
            },
            {
                'name': 'Bybit永续合约估算',
                'method': self._get_bybit_futures_estimate,
                'weight': 0.85,
                'timeout': 10
            }
        ]
        
        # 价格历史记录（用于异常检测）
        self.price_history = []
        self.max_history = 10
        
    def get_mt5_equivalent_price(self) -> Optional[float]:
        """获取MT5等效价格"""
        cache_key = 'mt5_price'
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            cached_price = self.price_cache[cache_key]['price']
            self.logger.info(f"使用缓存的MT5价格: {cached_price}")
            return cached_price
        
        # 尝试各个价格源
        for source in self.price_sources:
            try:
                self.logger.info(f"尝试获取价格: {source['name']}")
                price = source['method']()
                
                if price and self._validate_price(price):
                    # 应用权重调整
                    adjusted_price = price * source['weight']
                    
                    # 缓存价格
                    self._cache_price(cache_key, adjusted_price)
                    
                    # 记录价格历史
                    self._record_price(adjusted_price, source['name'])
                    
                    self.logger.info(f"✅ 成功获取MT5等效价格: {adjusted_price} (来源: {source['name']})")
                    return adjusted_price
                    
            except Exception as e:
                self.logger.warning(f"价格源 {source['name']} 失败: {e}")
                continue
        
        # 如果所有价格源都失败，尝试使用历史价格估算
        estimated_price = self._get_estimated_price()
        if estimated_price:
            self.logger.warning(f"⚠️ 使用历史价格估算: {estimated_price}")
            return estimated_price
        
        self.logger.error("❌ 所有价格源都失败，无法获取MT5价格")
        return None
    
    def _get_bybit_spot_price(self) -> Optional[float]:
        """获取Bybit现货XAUUSDT价格"""
        response = requests.get(
            'https://api.bybit.com/v5/market/tickers',
            params={'category': 'spot', 'symbol': 'XAUUSDT'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                ticker = data['result']['list'][0]
                return float(ticker['lastPrice'])
        return None
    
    def _get_yahoo_gold_price(self) -> Optional[float]:
        """获取Yahoo Finance黄金期货价格"""
        response = requests.get(
            'https://query1.finance.yahoo.com/v8/finance/chart/GC=F',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('chart', {}).get('result', [])
            if result:
                meta = result[0].get('meta', {})
                price = meta.get('regularMarketPrice')
                if price:
                    return float(price)
        return None
    
    def _get_coingecko_price(self) -> Optional[float]:
        """获取CoinGecko黄金价格"""
        response = requests.get(
            'https://api.coingecko.com/api/v3/simple/price',
            params={'ids': 'gold', 'vs_currencies': 'usd'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'gold' in data and 'usd' in data['gold']:
                return float(data['gold']['usd'])
        return None
    
    def _get_bybit_futures_estimate(self) -> Optional[float]:
        """基于Bybit永续合约估算MT5价格"""
        response = requests.get(
            'https://api.bybit.com/v5/market/tickers',
            params={'category': 'linear', 'symbol': 'XAUTUSDT'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                ticker = data['result']['list'][0]
                bybit_price = float(ticker['lastPrice'])
                
                # MT5通常比Bybit永续合约价格高0.1-0.3%
                estimated_price = bybit_price * 1.002  # 加0.2%的典型价差
                return estimated_price
        return None
    
    def _validate_price(self, price: float) -> bool:
        """验证价格是否合理"""
        if not price or price <= 0:
            return False
        
        # 黄金价格合理范围检查
        if price < 1000 or price > 5000:
            self.logger.warning(f"价格超出合理范围: {price}")
            return False
        
        # 与历史价格比较（避免异常波动）
        if self.price_history:
            recent_avg = sum(self.price_history[-3:]) / min(len(self.price_history), 3)
            price_change = abs(price - recent_avg) / recent_avg
            
            if price_change > 0.05:  # 5%的异常波动阈值
                self.logger.warning(f"价格波动异常: {price} vs 平均 {recent_avg} (变化 {price_change:.2%})")
                return False
        
        return True
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.price_cache:
            return False
        
        cache_time = self.price_cache[cache_key]['timestamp']
        return datetime.now() - cache_time < timedelta(seconds=self.cache_duration)
    
    def _cache_price(self, cache_key: str, price: float):
        """缓存价格"""
        self.price_cache[cache_key] = {
            'price': price,
            'timestamp': datetime.now()
        }
    
    def _record_price(self, price: float, source: str):
        """记录价格历史"""
        self.price_history.append(price)
        if len(self.price_history) > self.max_history:
            self.price_history.pop(0)
        
        self.logger.debug(f"记录价格: {price} (来源: {source})")
    
    def _get_estimated_price(self) -> Optional[float]:
        """基于历史价格估算当前价格"""
        if not self.price_history:
            return None
        
        # 使用最近3个价格的平均值
        recent_prices = self.price_history[-3:]
        estimated = sum(recent_prices) / len(recent_prices)
        
        self.logger.info(f"基于历史价格估算: {estimated} (基于 {len(recent_prices)} 个历史价格)")
        return estimated
    
    def get_price_status(self) -> Dict:
        """获取价格管理器状态"""
        return {
            'cache_size': len(self.price_cache),
            'history_size': len(self.price_history),
            'last_prices': self.price_history[-3:] if self.price_history else [],
            'available_sources': len(self.price_sources)
        }

# 全局价格管理器实例
_price_manager = None

def get_cloud_price_manager() -> CloudPriceManager:
    """获取全局价格管理器实例"""
    global _price_manager
    if _price_manager is None:
        _price_manager = CloudPriceManager()
    return _price_manager
