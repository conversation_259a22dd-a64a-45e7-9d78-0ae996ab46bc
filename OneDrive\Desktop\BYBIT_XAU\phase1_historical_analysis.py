"""
第一階段：BYBIT XAUTUSDT vs BYBIT MT5 XAUUSD+ 歷史數據分析
獲取近7個交易日的每分鐘收盤價，計算差價分布
"""
import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import pytz
from dotenv import load_dotenv
import requests
import MetaTrader5 as mt5
import time

# 載入環境變數
load_dotenv('config.env')

class XAUArbitrageHistoricalAnalysis:
    def __init__(self):
        """初始化歷史分析系統"""
        self.bybit_api_key = os.getenv('BYBIT_API_KEY')
        self.bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        # MT5 設定
        self.mt5_login = int(os.getenv('BYBIT_MT5_LOGIN'))
        self.mt5_password = os.getenv('BYBIT_MT5_PASSWORD')
        self.mt5_server = os.getenv('BYBIT_MT5_SERVER')
        self.mt5_path = os.getenv('BYBIT_MT5_PATH')
        
        # 時區設定
        self.timezone = pytz.timezone('Asia/Taipei')
        
        # 數據存儲
        self.bybit_data = None
        self.mt5_data = None
        self.spread_data = None
        
        print("XAU套利歷史分析系統初始化完成")
    
    def get_bybit_historical_data(self, days=7):
        """獲取Bybit XAUTUSDT永續合約歷史數據"""
        print(f"正在獲取Bybit XAUTUSDT近{days}天的1分鐘K線數據...")
        
        # 計算時間範圍
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # 轉換為毫秒時間戳
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(end_time.timestamp() * 1000)
        
        url = "https://api.bybit.com/v5/market/kline"
        
        all_data = []
        current_start = start_timestamp
        
        while current_start < end_timestamp:
            params = {
                'category': 'linear',
                'symbol': 'XAUTUSDT',
                'interval': '1',  # 1分鐘
                'start': current_start,
                'end': min(current_start + 200 * 60 * 1000, end_timestamp),  # 每次最多200根K線
                'limit': 200
            }
            
            try:
                response = requests.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if data['retCode'] == 0 and data['result']['list']:
                    klines = data['result']['list']
                    all_data.extend(klines)
                    print(f"已獲取 {len(klines)} 根K線，總計 {len(all_data)} 根")
                    
                    # 更新下一批的開始時間
                    last_timestamp = int(klines[-1][0])
                    current_start = last_timestamp + 60000  # 下一分鐘
                else:
                    print(f"API返回錯誤: {data}")
                    break
                    
                time.sleep(0.1)  # 避免API限制
                
            except Exception as e:
                print(f"獲取Bybit數據時發生錯誤: {e}")
                break
        
        if all_data:
            # 轉換為DataFrame
            df = pd.DataFrame(all_data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
            ])
            
            # 轉換數據類型
            df['timestamp'] = pd.to_datetime(df['timestamp'].astype(int), unit='ms')
            df['close'] = df['close'].astype(float)
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 設定時區
            df['timestamp'] = df['timestamp'].dt.tz_localize('UTC').dt.tz_convert(self.timezone)
            
            self.bybit_data = df[['timestamp', 'close']].copy()
            self.bybit_data.columns = ['time', 'bybit_price']
            
            print(f"Bybit數據獲取完成: {len(self.bybit_data)} 筆記錄")
            print(f"時間範圍: {self.bybit_data['time'].min()} 到 {self.bybit_data['time'].max()}")
            return True
        else:
            print("未能獲取Bybit數據")
            return False
    
    def get_mt5_historical_data(self, days=7):
        """獲取MT5 XAUUSD+歷史數據"""
        print(f"正在獲取MT5 XAUUSD+近{days}天的1分鐘數據...")
        
        # 初始化MT5
        if not mt5.initialize(path=self.mt5_path):
            print(f"MT5初始化失敗: {mt5.last_error()}")
            return False
        
        # 登入MT5
        if not mt5.login(self.mt5_login, password=self.mt5_password, server=self.mt5_server):
            print(f"MT5登入失敗: {mt5.last_error()}")
            mt5.shutdown()
            return False
        
        print("MT5連接成功")
        
        # 獲取歷史數據
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # 獲取1分鐘K線數據
        rates = mt5.copy_rates_range("XAUUSD+", mt5.TIMEFRAME_M1, start_time, end_time)
        
        mt5.shutdown()
        
        if rates is not None and len(rates) > 0:
            # 轉換為DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            # 設定時區
            df['time'] = df['time'].dt.tz_localize('UTC').dt.tz_convert(self.timezone)
            
            self.mt5_data = df[['time', 'close']].copy()
            self.mt5_data.columns = ['time', 'mt5_price']
            
            print(f"MT5數據獲取完成: {len(self.mt5_data)} 筆記錄")
            print(f"時間範圍: {self.mt5_data['time'].min()} 到 {self.mt5_data['time'].max()}")
            return True
        else:
            print("未能獲取MT5數據")
            return False
    
    def merge_and_calculate_spreads(self):
        """合併數據並計算差價"""
        if self.bybit_data is None or self.mt5_data is None:
            print("缺少必要的價格數據")
            return False
        
        print("正在合併數據並計算差價...")
        
        # 合併數據 (使用時間戳對齊)
        merged = pd.merge_asof(
            self.bybit_data.sort_values('time'),
            self.mt5_data.sort_values('time'),
            on='time',
            direction='nearest',
            tolerance=pd.Timedelta('2 minutes')  # 允許2分鐘的時間差
        )
        
        # 移除缺失數據
        merged = merged.dropna()
        
        if len(merged) == 0:
            print("合併後沒有有效數據")
            return False
        
        # 計算差價
        merged['price_diff'] = merged['bybit_price'] - merged['mt5_price']
        merged['price_diff_pct'] = (merged['price_diff'] / merged['mt5_price']) * 100
        
        # 添加日期列
        merged['date'] = merged['time'].dt.date
        
        self.spread_data = merged
        
        print(f"數據合併完成: {len(self.spread_data)} 筆有效記錄")
        return True
    
    def analyze_spread_distribution(self):
        """分析差價分布"""
        if self.spread_data is None:
            print("沒有差價數據可分析")
            return
        
        print("="*80)
        print("BYBIT XAUTUSDT vs BYBIT MT5 XAUUSD+ 差價分析報告")
        print("="*80)
        
        # 基本統計
        spread_pct = self.spread_data['price_diff_pct']
        
        print(f"\n📊 基本統計:")
        print(f"數據筆數: {len(self.spread_data):,}")
        print(f"時間範圍: {self.spread_data['time'].min()} 到 {self.spread_data['time'].max()}")
        print(f"最大正差價: {spread_pct.max():.6f}%")
        print(f"最小負差價: {spread_pct.min():.6f}%")
        print(f"平均差價: {spread_pct.mean():.6f}%")
        print(f"標準差: {spread_pct.std():.6f}%")
        print(f"中位數: {spread_pct.median():.6f}%")
        
        # 正負差價分布
        positive_spreads = spread_pct[spread_pct > 0]
        negative_spreads = spread_pct[spread_pct < 0]
        zero_spreads = spread_pct[spread_pct == 0]
        
        print(f"\n📈 差價方向分布:")
        print(f"正差價 (Bybit > MT5): {len(positive_spreads):,} 次 ({len(positive_spreads)/len(spread_pct)*100:.2f}%)")
        print(f"負差價 (Bybit < MT5): {len(negative_spreads):,} 次 ({len(negative_spreads)/len(spread_pct)*100:.2f}%)")
        print(f"零差價: {len(zero_spreads):,} 次 ({len(zero_spreads)/len(spread_pct)*100:.2f}%)")
        
        # 差價閾值分析
        print(f"\n🎯 不同閾值下的套利機會:")
        print("-"*80)
        print(f"{'差價閾值':<12} {'正差價機會':<10} {'負差價機會':<10} {'總機會':<8} {'佔比':<8}")
        print("-"*80)
        
        thresholds = [0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5]
        
        for threshold in thresholds:
            pos_count = len(positive_spreads[positive_spreads >= threshold])
            neg_count = len(negative_spreads[negative_spreads <= -threshold])
            total_count = pos_count + neg_count
            percentage = total_count / len(spread_pct) * 100
            
            print(f"> {threshold:.3f}%{'':<5} {pos_count:<10} {neg_count:<10} {total_count:<8} {percentage:>6.2f}%")
        
        # 百分位數分析
        print(f"\n📊 百分位數分析:")
        print("-"*50)
        
        percentiles = [99.9, 99.5, 99, 98, 95, 90, 75, 50, 25, 10, 5, 2, 1, 0.5, 0.1]
        
        for p in percentiles:
            value = np.percentile(spread_pct, p)
            print(f"{p:5.1f}百分位: {value:8.6f}%")
        
        # 每日統計
        print(f"\n📅 每日差價統計:")
        print("-"*70)
        print(f"{'日期':<12} {'記錄數':<8} {'最大正差價':<12} {'最小負差價':<12} {'平均差價':<10}")
        print("-"*70)
        
        daily_stats = self.spread_data.groupby('date').agg({
            'price_diff_pct': ['count', 'max', 'min', 'mean']
        }).round(6)
        
        for date, row in daily_stats.iterrows():
            count = int(row[('price_diff_pct', 'count')])
            max_spread = row[('price_diff_pct', 'max')]
            min_spread = row[('price_diff_pct', 'min')]
            avg_spread = row[('price_diff_pct', 'mean')]
            
            print(f"{date:<12} {count:<8} {max_spread:>10.6f}% {min_spread:>10.6f}% {avg_spread:>8.6f}%")
        
        # 保存數據
        self.save_results()
    
    def save_results(self):
        """保存分析結果"""
        os.makedirs('data', exist_ok=True)
        
        # 保存原始數據
        if self.spread_data is not None:
            self.spread_data.to_csv('data/xau_spread_analysis.csv', index=False)
            print(f"\n✅ 差價數據已保存到: data/xau_spread_analysis.csv")
        
        # 保存統計摘要
        if self.spread_data is not None:
            spread_pct = self.spread_data['price_diff_pct']
            
            summary = {
                'total_records': len(self.spread_data),
                'max_positive_spread': spread_pct.max(),
                'min_negative_spread': spread_pct.min(),
                'mean_spread': spread_pct.mean(),
                'std_spread': spread_pct.std(),
                'median_spread': spread_pct.median(),
                'positive_count': len(spread_pct[spread_pct > 0]),
                'negative_count': len(spread_pct[spread_pct < 0]),
                'zero_count': len(spread_pct[spread_pct == 0])
            }
            
            summary_df = pd.DataFrame([summary])
            summary_df.to_csv('data/xau_spread_summary.csv', index=False)
            print(f"✅ 統計摘要已保存到: data/xau_spread_summary.csv")
    
    def run_analysis(self, days=7):
        """執行完整的歷史分析"""
        print("開始執行XAU套利歷史分析...")
        
        # 步驟1: 獲取Bybit數據
        if not self.get_bybit_historical_data(days):
            print("❌ Bybit數據獲取失敗")
            return False
        
        # 步驟2: 獲取MT5數據
        if not self.get_mt5_historical_data(days):
            print("❌ MT5數據獲取失敗")
            return False
        
        # 步驟3: 合併數據並計算差價
        if not self.merge_and_calculate_spreads():
            print("❌ 數據合併失敗")
            return False
        
        # 步驟4: 分析差價分布
        self.analyze_spread_distribution()
        
        print("\n🎉 歷史分析完成！")
        return True

def main():
    """主函數"""
    analyzer = XAUArbitrageHistoricalAnalysis()
    
    # 執行7天的歷史分析
    success = analyzer.run_analysis(days=7)
    
    if success:
        print("\n✅ 第一階段歷史數據分析完成")
        print("📁 結果文件:")
        print("   - data/xau_spread_analysis.csv (詳細數據)")
        print("   - data/xau_spread_summary.csv (統計摘要)")
    else:
        print("\n❌ 分析失敗，請檢查配置和網絡連接")

if __name__ == "__main__":
    main()
