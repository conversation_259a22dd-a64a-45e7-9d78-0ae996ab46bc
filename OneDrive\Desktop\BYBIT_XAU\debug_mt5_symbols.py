# -*- coding: utf-8 -*-
import MetaTrader5 as mt5
import os
from dotenv import load_dotenv

load_dotenv('config.env')

def debug_mt5_symbols():
    """調試 MT5 交易對"""
    print("🔍 調試 Bybit MT5 交易對...")
    
    # 初始化 MT5
    login = os.getenv('BYBIT_MT5_LOGIN')
    password = os.getenv('BYBIT_MT5_PASSWORD')
    server = os.getenv('BYBIT_MT5_SERVER', 'Bybit-Live')
    path = os.getenv('BYBIT_MT5_PATH', 'C:\\Program Files\\MetaTrader 5\\terminal64.exe')
    
    print(f"連接資訊:")
    print(f"  伺服器: {server}")
    print(f"  帳號: {login}")
    print(f"  路徑: {path}")
    
    # 初始化 MT5
    if not mt5.initialize(path=path):
        print(f"❌ MT5 初始化失敗: {mt5.last_error()}")
        return
    
    # 登入
    if not mt5.login(login=int(login), password=password, server=server):
        print(f"❌ MT5 登入失敗: {mt5.last_error()}")
        return
    
    print("✅ MT5 連接成功")
    
    # 獲取所有交易對
    symbols = mt5.symbols_get()
    if symbols is None:
        print("❌ 無法獲取交易對列表")
        return
    
    print(f"📋 總共有 {len(symbols)} 個交易對")
    
    # 搜尋黃金相關的交易對
    gold_symbols = []
    for symbol in symbols:
        if hasattr(symbol, 'name') and symbol.name:
            if 'XAU' in symbol.name or 'GOLD' in symbol.name:
                gold_symbols.append(symbol.name)
    
    print(f"\n🏆 找到 {len(gold_symbols)} 個黃金相關交易對:")
    for symbol in gold_symbols:
        print(f"  - {symbol}")
    
    # 測試選擇 XAUUSD
    print(f"\n🧪 測試選擇 XAUUSD...")
    if mt5.symbol_select("XAUUSD", True):
        print("✅ XAUUSD 選擇成功")
        
        # 獲取報價
        tick = mt5.symbol_info_tick("XAUUSD")
        if tick:
            print(f"✅ XAUUSD 報價獲取成功:")
            print(f"   買價: {tick.bid}")
            print(f"   賣價: {tick.ask}")
            print(f"   最新價: {tick.last}")
            print(f"   時間: {tick.time}")
        else:
            print("❌ XAUUSD 報價獲取失敗")
    else:
        print(f"❌ XAUUSD 選擇失敗: {mt5.last_error()}")
    
    # 測試其他可能的黃金代號
    test_symbols = ['XAUUSD+', 'XAUUSD.m', 'GOLD', 'GOLDUSD']
    print(f"\n🧪 測試其他可能的黃金代號...")
    
    for symbol in test_symbols:
        print(f"\n測試 {symbol}:")
        if mt5.symbol_select(symbol, True):
            print(f"✅ {symbol} 選擇成功")
            tick = mt5.symbol_info_tick(symbol)
            if tick:
                print(f"   買價: {tick.bid}, 賣價: {tick.ask}, 最新價: {tick.last}")
            else:
                print(f"   報價獲取失敗")
        else:
            print(f"❌ {symbol} 選擇失敗: {mt5.last_error()}")
    
    # 顯示前20個交易對作為參考
    print(f"\n📋 前20個交易對作為參考:")
    for i, symbol in enumerate(symbols[:20]):
        if hasattr(symbol, 'name') and symbol.name:
            print(f"  {i+1:2d}. {symbol.name}")
    
    # 清理
    mt5.shutdown()
    print("\n🔌 MT5 連接已斷開")

if __name__ == "__main__":
    debug_mt5_symbols() 