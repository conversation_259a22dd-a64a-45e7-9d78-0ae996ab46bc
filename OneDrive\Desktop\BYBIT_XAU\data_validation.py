"""
數據驗證 - 重新檢查歷史數據是否正確
檢查價差計算、時間同步、數據質量等問題
"""
import pandas as pd
import numpy as np
from datetime import datetime

def validate_historical_data():
    """驗證歷史數據"""
    
    # 載入數據
    try:
        data = pd.read_csv('data/xau_spread_analysis_simple.csv')
        data['time'] = pd.to_datetime(data['time'])
        data = data.sort_values('time').reset_index(drop=True)
        print(f"✅ 成功載入 {len(data)} 筆數據")
    except FileNotFoundError:
        print("❌ 找不到數據文件")
        return
    
    print(f"\n📊 數據基本信息:")
    print(f"   時間範圍: {data['time'].min()} 到 {data['time'].max()}")
    print(f"   總天數: {(data['time'].max() - data['time'].min()).days + 1} 天")
    print(f"   數據點數: {len(data)} 個")
    
    # 檢查數據列
    print(f"\n📋 數據列:")
    for col in data.columns:
        print(f"   {col}: {data[col].dtype}")
    
    # 檢查前10筆數據
    print(f"\n🔍 前10筆數據:")
    print(data.head(10).to_string())
    
    # 檢查價差計算是否正確
    print(f"\n🧮 價差計算驗證:")
    for i in range(5):
        row = data.iloc[i]
        bybit_price = row['bybit_price']
        mt5_price = row['mt5_price']
        recorded_spread = row['price_diff_pct']
        
        # 重新計算價差
        calculated_spread = ((bybit_price - mt5_price) / mt5_price) * 100
        
        print(f"   第{i+1}筆: BYBIT={bybit_price:.2f}, MT5={mt5_price:.2f}")
        print(f"          記錄差價={recorded_spread:.4f}%, 計算差價={calculated_spread:.4f}%")
        print(f"          差異={abs(recorded_spread - calculated_spread):.6f}%")
    
    # 檢查極端值
    print(f"\n📈 價差統計:")
    print(f"   最小差價: {data['price_diff_pct'].min():.4f}%")
    print(f"   最大差價: {data['price_diff_pct'].max():.4f}%")
    print(f"   平均差價: {data['price_diff_pct'].mean():.4f}%")
    print(f"   標準差: {data['price_diff_pct'].std():.4f}%")
    
    # 檢查-0.85%以下的數據點
    extreme_negative = data[data['price_diff_pct'] <= -0.85]
    print(f"\n🔍 差價 <= -0.85% 的數據點 ({len(extreme_negative)}個):")
    
    if len(extreme_negative) > 0:
        print("   時間 | BYBIT價格 | MT5價格 | 記錄差價 | 重算差價")
        print("-" * 70)
        
        for i, row in extreme_negative.head(20).iterrows():  # 只顯示前20個
            bybit_price = row['bybit_price']
            mt5_price = row['mt5_price']
            recorded_spread = row['price_diff_pct']
            calculated_spread = ((bybit_price - mt5_price) / mt5_price) * 100
            
            print(f"   {row['time']} | {bybit_price:8.2f} | {mt5_price:8.2f} | {recorded_spread:8.4f}% | {calculated_spread:8.4f}%")
    
    # 檢查價格的合理性
    print(f"\n💰 價格合理性檢查:")
    print(f"   BYBIT價格範圍: {data['bybit_price'].min():.2f} - {data['bybit_price'].max():.2f}")
    print(f"   MT5價格範圍: {data['mt5_price'].min():.2f} - {data['mt5_price'].max():.2f}")
    
    # 檢查是否有異常的價格跳躍
    data['bybit_price_change'] = data['bybit_price'].diff().abs()
    data['mt5_price_change'] = data['mt5_price'].diff().abs()
    
    large_bybit_changes = data[data['bybit_price_change'] > 50]  # 價格變化超過50美元
    large_mt5_changes = data[data['mt5_price_change'] > 50]
    
    if len(large_bybit_changes) > 0:
        print(f"\n⚠️ BYBIT價格異常跳躍 ({len(large_bybit_changes)}個):")
        for i, row in large_bybit_changes.head(5).iterrows():
            prev_price = data.iloc[i-1]['bybit_price'] if i > 0 else 0
            print(f"   {row['time']}: {prev_price:.2f} -> {row['bybit_price']:.2f} (變化: {row['bybit_price_change']:.2f})")
    
    if len(large_mt5_changes) > 0:
        print(f"\n⚠️ MT5價格異常跳躍 ({len(large_mt5_changes)}個):")
        for i, row in large_mt5_changes.head(5).iterrows():
            prev_price = data.iloc[i-1]['mt5_price'] if i > 0 else 0
            print(f"   {row['time']}: {prev_price:.2f} -> {row['mt5_price']:.2f} (變化: {row['mt5_price_change']:.2f})")
    
    # 檢查時間間隔
    data['time_diff'] = data['time'].diff()
    print(f"\n⏰ 時間間隔檢查:")
    print(f"   平均間隔: {data['time_diff'].mean()}")
    print(f"   最小間隔: {data['time_diff'].min()}")
    print(f"   最大間隔: {data['time_diff'].max()}")
    
    # 檢查是否有重複時間
    duplicate_times = data[data.duplicated('time', keep=False)]
    if len(duplicate_times) > 0:
        print(f"\n⚠️ 發現重複時間 ({len(duplicate_times)}個):")
        print(duplicate_times[['time', 'bybit_price', 'mt5_price', 'price_diff_pct']].head(10))
    
    # 檢查數據的連續性
    print(f"\n📅 按日期統計數據點:")
    data['date'] = data['time'].dt.date
    daily_counts = data.groupby('date').size()
    for date, count in daily_counts.items():
        print(f"   {date}: {count}個數據點")
    
    # 檢查是否有明顯的數據錯誤
    print(f"\n🔍 數據質量檢查:")
    
    # 檢查是否有空值
    null_counts = data.isnull().sum()
    if null_counts.sum() > 0:
        print("   發現空值:")
        for col, count in null_counts.items():
            if count > 0:
                print(f"     {col}: {count}個空值")
    else:
        print("   ✅ 沒有空值")
    
    # 檢查是否有零值或負值價格
    zero_bybit = len(data[data['bybit_price'] <= 0])
    zero_mt5 = len(data[data['mt5_price'] <= 0])
    
    if zero_bybit > 0:
        print(f"   ⚠️ BYBIT價格 <= 0: {zero_bybit}個")
    if zero_mt5 > 0:
        print(f"   ⚠️ MT5價格 <= 0: {zero_mt5}個")
    
    if zero_bybit == 0 and zero_mt5 == 0:
        print("   ✅ 所有價格都為正值")
    
    # 最後總結
    print(f"\n📋 驗證總結:")
    print(f"   數據時間範圍: {data['time'].min()} - {data['time'].max()}")
    print(f"   總數據點: {len(data)}")
    print(f"   差價 <= -0.85%的點: {len(extreme_negative)}")
    print(f"   平均每天數據點: {len(data) / ((data['time'].max() - data['time'].min()).days + 1):.1f}")
    
    if len(extreme_negative) > 0:
        print(f"   平均每天-0.85%機會: {len(extreme_negative) / ((data['time'].max() - data['time'].min()).days + 1):.1f}")
    
    return data, extreme_negative

def main():
    validate_historical_data()

if __name__ == "__main__":
    main()
