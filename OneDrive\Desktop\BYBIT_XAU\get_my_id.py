#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
獲取 Telegram User ID 的腳本
"""

import os
import requests
from dotenv import load_dotenv

load_dotenv('config.env')

def get_my_id():
    """獲取自己的 User ID"""
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    
    print("🔍 獲取 Telegram User ID")
    print("=" * 40)
    print("請按照以下步驟操作：")
    print("1. 在 Telegram 中搜尋你的 Bot: @fatsolerc_bot")
    print("2. 點擊 'Start' 或發送 /start")
    print("3. 然後運行這個腳本")
    print("=" * 40)
    
    input("按 Enter 繼續...")
    
    url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            updates = data.get('result', [])
            
            if updates:
                print("✅ 找到更新記錄：")
                for update in updates:
                    if 'message' in update:
                        message = update['message']
                        chat = message.get('chat', {})
                        user = message.get('from', {})
                        
                        print(f"Chat ID: {chat.get('id')}")
                        print(f"Chat Type: {chat.get('type')}")
                        print(f"User ID: {user.get('id')}")
                        print(f"Username: {user.get('username')}")
                        print(f"First Name: {user.get('first_name')}")
                        print("-" * 30)
            else:
                print("❌ 沒有找到更新記錄")
                print("請確保你已經發送訊息給 Bot")
        else:
            print(f"❌ 請求失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 錯誤: {e}")

if __name__ == "__main__":
    get_my_id() 