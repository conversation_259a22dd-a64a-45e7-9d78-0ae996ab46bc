#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 通知模組
----------------
發送交易記錄、套利機會等通知到 Telegram
"""

import os
import requests
from datetime import datetime
from typing import Optional, Dict, Any
from dotenv import load_dotenv

load_dotenv('config.env')

class TelegramNotifier:
    def __init__(self):
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
    def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """發送訊息到 Telegram"""
        if not self.bot_token or not self.chat_id:
            print("❌ Telegram 設定不完整")
            return False
        
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                return True
            else:
                print(f"❌ Telegram 發送失敗: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Telegram 發送錯誤: {e}")
            return False
    
    def send_arbitrage_opportunity(self, bybit_price: float, mt5_price: float, 
                                 spread: float, percentage_spread: float, 
                                 funding_rate: float) -> bool:
        """發送套利機會通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        message = (
            f"🚨 <b>套利機會偵測</b> 🚨\n"
            f"⏰ <b>時間:</b> {timestamp}\n"
            f"💰 <b>Bybit XAUTUSDT:</b> {bybit_price:.2f}\n"
            f"🏆 <b>MT5 XAUUSD+:</b> {mt5_price:.2f}\n"
            f"📊 <b>絕對價差:</b> {spread:.2f}\n"
            f"📈 <b>百分比價差:</b> {percentage_spread:.3f}%\n"
            f"💸 <b>資金費率:</b> {funding_rate:.6f}\n"
            f"🎯 <b>套利條件:</b>\n"
            f"• 價差 > 0.4%: {'✅' if abs(percentage_spread) > 0.4 else '❌'}\n"
            f"• 資金費率 < 0: {'✅' if funding_rate < 0 else '❌'}\n"
            f"📋 <b>建議策略:</b>\n"
            f"{'• 賣出 MT5 XAUUSD+ → 買入 Bybit XAUTUSDT' if percentage_spread > 0 else '• 買入 MT5 XAUUSD+ → 賣出 Bybit XAUTUSDT'}"
        )
        return self.send_message(message)
    
    def send_trade_execution(self, trade_info: Dict[str, Any]) -> bool:
        """發送交易執行通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        message = f"""
🎯 <b>套利交易執行</b> 🎯

⏰ <b>時間:</b> {timestamp}
📊 <b>交易 ID:</b> {trade_info.get('trade_id', 'N/A')}

<b>Bybit 永續合約:</b>
• 方向: {trade_info.get('bybit_side', 'N/A')}
• 數量: {trade_info.get('bybit_qty', 0):.3f}
• 價格: {trade_info.get('bybit_price', 0):.2f}
• 保證金: {trade_info.get('bybit_margin', 0):.2f} USDT
• 手續費: {trade_info.get('bybit_fee', 0):.4f} USDT

<b>MT5 現貨:</b>
• 方向: {trade_info.get('mt5_side', 'N/A')}
• 數量: {trade_info.get('mt5_qty', 0):.2f}
• 價格: {trade_info.get('mt5_price', 0):.2f}
• 保證金: {trade_info.get('mt5_margin', 0):.2f} USD
• 手續費: {trade_info.get('mt5_fee', 0):.4f} USD

💰 <b>總投資:</b> {trade_info.get('total_margin', 0):.2f}
📈 <b>預期收益:</b> {trade_info.get('expected_profit', 0):.2f}
"""
        
        return self.send_message(message)
    
    def send_trade_close(self, close_info: Dict[str, Any]) -> bool:
        """發送平倉通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        message = f"""
✅ <b>套利交易平倉</b> ✅

⏰ <b>時間:</b> {timestamp}
📊 <b>交易 ID:</b> {close_info.get('trade_id', 'N/A')}

<b>Bybit 永續合約:</b>
• 平倉價格: {close_info.get('bybit_close_price', 0):.2f}
• 收益: {close_info.get('bybit_pnl', 0):.2f} USDT

<b>MT5 現貨:</b>
• 平倉價格: {close_info.get('mt5_close_price', 0):.2f}
• 收益: {close_info.get('mt5_pnl', 0):.2f} USD

💰 <b>總收益:</b> {close_info.get('total_pnl', 0):.2f}
📊 <b>收益率:</b> {close_info.get('roi', 0):.2f}%
⏱️ <b>持倉時間:</b> {close_info.get('duration', 'N/A')}
"""
        
        return self.send_message(message)
    
    def send_error_notification(self, error_msg: str, context: str = "") -> bool:
        """發送錯誤通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        message = f"""
⚠️ <b>系統錯誤通知</b> ⚠️

⏰ <b>時間:</b> {timestamp}
🔍 <b>錯誤內容:</b> {error_msg}
📋 <b>錯誤位置:</b> {context}

請立即檢查系統狀態！
"""
        
        return self.send_message(message)
    
    def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """發送每日總結"""
        timestamp = datetime.now().strftime('%Y-%m-%d')
        
        message = f"""
📊 <b>每日套利總結</b> 📊

📅 <b>日期:</b> {timestamp}

💰 <b>交易統計:</b>
• 總交易次數: {summary_data.get('total_trades', 0)}
• 成功交易: {summary_data.get('successful_trades', 0)}
• 失敗交易: {summary_data.get('failed_trades', 0)}

💵 <b>收益統計:</b>
• 總收益: {summary_data.get('total_pnl', 0):.2f}
• 平均收益: {summary_data.get('avg_pnl', 0):.2f}
• 最大收益: {summary_data.get('max_pnl', 0):.2f}
• 最大虧損: {summary_data.get('max_loss', 0):.2f}

📈 <b>價差統計:</b>
• 平均價差: {summary_data.get('avg_spread', 0):.3f}%
• 最大價差: {summary_data.get('max_spread', 0):.3f}%
• 最小價差: {summary_data.get('min_spread', 0):.3f}%
"""
        
        return self.send_message(message)
    
    def test_connection(self) -> bool:
        """測試 Telegram 連接"""
        print("🔍 測試 Telegram 連接...")
        
        if not self.bot_token or not self.chat_id:
            print("❌ Telegram 設定不完整")
            print("請在 config.env 中設定:")
            print("TELEGRAM_BOT_TOKEN=您的機器人Token")
            print("TELEGRAM_CHAT_ID=您的聊天ID")
            return False
        
        test_message = "🤖 XAU 套利系統連接測試成功！"
        if self.send_message(test_message):
            print("✅ Telegram 連接測試成功")
            return True
        else:
            print("❌ Telegram 連接測試失敗")
            return False

# 測試函數
if __name__ == "__main__":
    notifier = TelegramNotifier()
    notifier.test_connection() 