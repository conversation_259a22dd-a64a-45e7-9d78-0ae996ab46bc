#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAU 套利交易系統
---------------
整合監控、交易執行和通知功能的完整套利系統
"""

import os
import time
import uuid
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

from bybit_futures_client import BybitFuturesClient
from bybit_mt5_client import BybitMT5Client
from telegram_notifier import TelegramNotifier

load_dotenv('config.env')

class XAUArbitrageTrader:
    def __init__(self):
        self.bybit_client = BybitFuturesClient()
        self.mt5_client = BybitMT5Client()
        self.notifier = TelegramNotifier()
        
        # 交易設定
        self.min_spread_threshold = 0.4  # 最小價差閾值 0.4%
        self.close_spread_threshold = 0.2  # 平倉價差閾值 0.2%
        self.leverage = 10  # 槓桿倍數
        self.position_size_ratio = 0.1  # 使用帳戶餘額的比例
        
        # 交易狀態
        self.active_trades = {}  # 活躍交易記錄
        self.trade_history = []  # 交易歷史
        
        # 數據檔案
        self.trades_file = 'data/arbitrage_trades.json'
        self.csv_file = 'xau_spread_data.csv'
        self.log_file = 'xau_arbitrage_trader.log'
        self.ensure_data_directory()
        
        # 初始化日誌
        self.setup_logging()
        
        # 初始化 CSV 檔案
        self.init_csv_file()
        
    def ensure_data_directory(self):
        """確保數據目錄存在"""
        os.makedirs('data', exist_ok=True)
    
    def setup_logging(self):
        """設置日誌記錄"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_csv_file(self):
        """初始化 CSV 檔案"""
        if not os.path.exists(self.csv_file):
            df = pd.DataFrame(columns=[
                'timestamp', 'bybit_xautusdt', 'mt5_xauusd', 
                'absolute_spread', 'percentage_spread', 'direction',
                'funding_rate', 'trade_triggered'
            ])
            df.to_csv(self.csv_file, index=False, encoding='utf-8')
            self.logger.info(f"初始化 CSV 檔案: {self.csv_file}")
    
    def log_spread_data(self, prices: Dict, spread_info: Dict, funding_rate: float, trade_triggered: bool = False):
        """記錄價差數據到 CSV"""
        try:
            timestamp = datetime.now()
            new_row = {
                'timestamp': timestamp,
                'bybit_xautusdt': prices.get('bybit', 0),
                'mt5_xauusd': prices.get('mt5', 0),
                'absolute_spread': spread_info['absolute'],
                'percentage_spread': spread_info['percentage'],
                'direction': spread_info['direction'],
                'funding_rate': funding_rate,
                'trade_triggered': trade_triggered
            }
            
            df = pd.read_csv(self.csv_file)
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            df.to_csv(self.csv_file, index=False, encoding='utf-8')
            
        except Exception as e:
            self.logger.error(f"記錄 CSV 數據失敗: {e}")
        
    def get_current_prices(self) -> Dict[str, float]:
        """獲取當前價格"""
        prices = {}
        
        # 獲取 Bybit 永續合約價格
        bybit_price = self.bybit_client.get_ticker_price("XAUTUSDT")
        if bybit_price:
            prices['bybit'] = bybit_price
        else:
            print("⚠️ 無法獲取 Bybit XAUTUSDT 價格")
        
        # 獲取 MT5 現貨價格
        if self.mt5_client.connect():
            tick = self.mt5_client.get_tick("XAUUSD+")
            self.mt5_client.disconnect()
            if tick:
                prices['mt5'] = (tick['bid'] + tick['ask']) / 2
            else:
                print("⚠️ 無法獲取 MT5 XAUUSD+ 價格")
        else:
            print("⚠️ MT5 連接失敗")
        
        return prices
    
    def calculate_spread(self, bybit_price: float, mt5_price: float) -> Dict[str, float]:
        """計算價差"""
        absolute_spread = mt5_price - bybit_price
        percentage_spread = (absolute_spread / bybit_price) * 100
        
        return {
            'absolute': absolute_spread,
            'percentage': percentage_spread,
            'direction': 'MT5 > Bybit' if percentage_spread > 0 else 'Bybit > MT5'
        }
    
    def check_arbitrage_conditions(self, spread_info: Dict, funding_rate: float) -> bool:
        """檢查套利條件"""
        # 價差大於 0.4%
        if abs(spread_info['percentage']) < self.min_spread_threshold:
            return False
        
        # 資金費率小於等於0
        if funding_rate > 0:
            return False
        
        return True
    
    def calculate_position_sizes(self, bybit_price: float, mt5_price: float) -> Dict[str, float]:
        """計算兩邊的持倉大小"""
        # 獲取帳戶餘額
        bybit_account = self.bybit_client.get_account_info()
        if not bybit_account:
            return {}
        
        mt5_account = None
        if self.mt5_client.connect():
            mt5_account = self.mt5_client.get_account_info()
            self.mt5_client.disconnect()
        
        if not mt5_account:
            return {}
        
        # 計算持倉大小
        bybit_balance = float(bybit_account.get('list', [{}])[0].get('totalWalletBalance', 0))
        mt5_balance = float(mt5_account.get('balance', 0))
        
        # 使用 10U 一倉，但確保不超過帳戶餘額
        target_position_value = 10.0  # 10U 一倉
        max_position_value = min(bybit_balance, mt5_balance) * self.leverage * 0.9  # 90% 安全邊際
        position_value = min(target_position_value, max_position_value)
        
        # 計算數量
        bybit_qty = position_value / bybit_price
        mt5_qty = position_value / mt5_price
        
        return {
            'bybit_qty': round(bybit_qty, 3),
            'mt5_qty': round(mt5_qty, 2),
            'position_value': position_value
        }
    
    def execute_arbitrage_trade(self, spread_info: Dict, prices: Dict, 
                               position_sizes: Dict) -> Optional[Dict]:
        """執行套利交易"""
        trade_id = str(uuid.uuid4())[:8]
        trade_start_time = datetime.now()
        
        print(f"🎯 開始執行套利交易 {trade_id}")
        
        # 決定交易方向
        if spread_info['percentage'] > 0:
            # MT5 價格較高，賣出 MT5，買入 Bybit
            mt5_side = "Sell"
            bybit_side = "Buy"
        else:
            # Bybit 價格較高，買入 MT5，賣出 Bybit
            mt5_side = "Buy"
            bybit_side = "Sell"
        
        trade_result = {
            'trade_id': trade_id,
            'start_time': trade_start_time,
            'bybit_side': bybit_side,
            'mt5_side': mt5_side,
            'bybit_qty': position_sizes['bybit_qty'],
            'mt5_qty': position_sizes['mt5_qty'],
            'bybit_price': prices['bybit'],
            'mt5_price': prices['mt5'],
            'spread': spread_info['percentage'],
            'status': 'executing'
        }
        
        try:
            # 執行 Bybit 訂單
            print(f"📊 執行 Bybit {bybit_side} 訂單...")
            bybit_order = self.bybit_client.place_order(
                symbol="XAUTUSDT",
                side=bybit_side,
                qty=position_sizes['bybit_qty']
            )
            
            if not bybit_order:
                print("❌ Bybit 訂單執行失敗")
                return None
            
            trade_result['bybit_order'] = bybit_order
            
            # 執行 MT5 訂單
            print(f"📊 執行 MT5 {mt5_side} 訂單...")
            if not self.mt5_client.connect():
                print("❌ MT5 連接失敗")
                return None
            
            mt5_order_type = 0 if mt5_side == "Buy" else 1  # 0=Buy, 1=Sell
            mt5_order = self.mt5_client.place_order(
                symbol="XAUUSD+",
                order_type=mt5_order_type,
                volume=position_sizes['mt5_qty'],
                comment=f"Arbitrage {trade_id}"
            )
            
            self.mt5_client.disconnect()
            
            if not mt5_order:
                print("❌ MT5 訂單執行失敗")
                return None
            
            trade_result['mt5_order'] = mt5_order
            trade_result['status'] = 'active'
            
            # 計算保證金和手續費
            bybit_margin = position_sizes['position_value'] / self.leverage
            mt5_margin = position_sizes['position_value'] / self.leverage
            
            # 估算手續費 (通常為 0.1%)
            bybit_fee = position_sizes['position_value'] * 0.001
            mt5_fee = position_sizes['position_value'] * 0.001
            
            trade_result.update({
                'bybit_margin': bybit_margin,
                'mt5_margin': mt5_margin,
                'bybit_fee': bybit_fee,
                'mt5_fee': mt5_fee,
                'total_margin': bybit_margin + mt5_margin
            })
            
            # 發送交易執行通知
            self.notifier.send_trade_execution(trade_result)
            
            print(f"✅ 套利交易 {trade_id} 執行成功")
            return trade_result
            
        except Exception as e:
            print(f"❌ 套利交易執行錯誤: {e}")
            self.notifier.send_error_notification(str(e), "套利交易執行")
            return None
    
    def check_close_conditions(self, trade_info: Dict) -> bool:
        """檢查平倉條件"""
        current_prices = self.get_current_prices()
        if not current_prices or len(current_prices) < 2:
            return False
        
        current_spread = self.calculate_spread(
            current_prices['bybit'], 
            current_prices['mt5']
        )
        
        # 價差小於 0.2% 時平倉
        return abs(current_spread['percentage']) <= self.close_spread_threshold
    
    def close_arbitrage_trade(self, trade_info: Dict) -> Optional[Dict]:
        """平倉套利交易"""
        trade_id = trade_info['trade_id']
        print(f"🔚 開始平倉交易 {trade_id}")
        
        current_prices = self.get_current_prices()
        if not current_prices or len(current_prices) < 2:
            print("❌ 無法獲取當前價格")
            return None
        
        close_result = {
            'trade_id': trade_id,
            'close_time': datetime.now(),
            'bybit_close_price': current_prices['bybit'],
            'mt5_close_price': current_prices['mt5']
        }
        
        try:
            # 平倉 Bybit 持倉
            print(f"📊 平倉 Bybit 持倉...")
            bybit_close_side = "Sell" if trade_info['bybit_side'] == "Buy" else "Buy"
            bybit_close_order = self.bybit_client.close_position(
                symbol="XAUTUSDT",
                side=bybit_close_side,
                qty=trade_info['bybit_qty']
            )
            
            if not bybit_close_order:
                print("❌ Bybit 平倉失敗")
                return None
            
            close_result['bybit_close_order'] = bybit_close_order
            
            # 平倉 MT5 持倉
            print(f"📊 平倉 MT5 持倉...")
            if not self.mt5_client.connect():
                print("❌ MT5 連接失敗")
                return None
            
            # 獲取 MT5 持倉
            positions = self.mt5_client.get_positions("XAUUSD+")
            self.mt5_client.disconnect()
            
            if not positions:
                print("❌ 找不到 MT5 持倉")
                return None
            
            # 平倉第一個找到的 XAUUSD+ 持倉
            mt5_position = positions[0]
            mt5_close_order = self.mt5_client.close_position(
                ticket=mt5_position['ticket']
            )
            
            if not mt5_close_order:
                print("❌ MT5 平倉失敗")
                return None
            
            close_result['mt5_close_order'] = mt5_close_order
            
            # 計算收益
            duration = close_result['close_time'] - trade_info['start_time']
            
            # 計算 PnL (簡化計算)
            bybit_pnl = (close_result['bybit_close_price'] - trade_info['bybit_price']) * trade_info['bybit_qty']
            if trade_info['bybit_side'] == "Sell":
                bybit_pnl = -bybit_pnl
            
            mt5_pnl = (close_result['mt5_close_price'] - trade_info['mt5_price']) * trade_info['mt5_qty']
            if trade_info['mt5_side'] == "Sell":
                mt5_pnl = -mt5_pnl
            
            total_pnl = bybit_pnl + mt5_pnl
            roi = (total_pnl / trade_info['total_margin']) * 100
            
            close_result.update({
                'bybit_pnl': bybit_pnl,
                'mt5_pnl': mt5_pnl,
                'total_pnl': total_pnl,
                'roi': roi,
                'duration': str(duration)
            })
            
            # 發送平倉通知
            self.notifier.send_trade_close(close_result)
            
            print(f"✅ 套利交易 {trade_id} 平倉成功")
            print(f"💰 總收益: {total_pnl:.2f} (ROI: {roi:.2f}%)")
            
            return close_result
            
        except Exception as e:
            print(f"❌ 平倉錯誤: {e}")
            self.notifier.send_error_notification(str(e), "套利交易平倉")
            return None
    
    def save_trade_data(self):
        """保存交易數據"""
        data = {
            'active_trades': self.active_trades,
            'trade_history': self.trade_history,
            'last_update': datetime.now().isoformat()
        }
        
        try:
            with open(self.trades_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            print(f"❌ 保存交易數據失敗: {e}")
    
    def load_trade_data(self):
        """載入交易數據"""
        if not os.path.exists(self.trades_file):
            return
        
        try:
            with open(self.trades_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.active_trades = data.get('active_trades', {})
                self.trade_history = data.get('trade_history', [])
        except Exception as e:
            print(f"❌ 載入交易數據失敗: {e}")
    
    def run_arbitrage_system(self, interval: int = 60):
        """運行套利系統"""
        print("🚀 啟動 XAU 套利交易系統")
        print(f"📊 監控間隔: {interval} 秒")
        print(f"🎯 進場價差: {self.min_spread_threshold}%")
        print(f"🔚 平倉價差: {self.close_spread_threshold}%")
        print(f"📈 槓桿倍數: {self.leverage}x")
        print("=" * 60)
        
        # 載入歷史數據
        self.load_trade_data()
        
        try:
            while True:
                current_time = datetime.now()
                print(f"\n[{current_time.strftime('%Y-%m-%d %H:%M:%S')}] 檢查套利機會...")
                
                # 獲取當前價格
                prices = self.get_current_prices()
                if len(prices) < 2:
                    print("❌ 風控機制：無法獲取完整價格數據，跳過本次檢查")
                    print(f"   已獲取價格: {list(prices.keys())}")
                    time.sleep(interval)
                    continue
                
                # 計算價差
                spread_info = self.calculate_spread(prices['bybit'], prices['mt5'])
                print(f"💰 Bybit: {prices['bybit']:.2f}, MT5: {prices['mt5']:.2f}")
                print(f"📊 價差: {spread_info['absolute']:.2f} ({spread_info['percentage']:.3f}%)")
                
                # 獲取資金費率
                funding_rate = self.bybit_client.get_funding_rate("XAUTUSDT")
                if funding_rate is not None:
                    print(f"💸 資金費率: {funding_rate:.6f}")
                
                # 記錄到 CSV 和日誌
                trade_triggered = False
                if not self.active_trades:  # 沒有活躍交易時才開新倉
                    if self.check_arbitrage_conditions(spread_info, funding_rate or 0):
                        trade_triggered = True
                
                self.log_spread_data(prices, spread_info, funding_rate or 0, trade_triggered)
                self.logger.info(f"價差監控 - Bybit: {prices['bybit']:.2f}, MT5: {prices['mt5']:.2f}, 價差: {spread_info['percentage']:.3f}%, 資金費率: {funding_rate or 0:.6f}")
                
                # 檢查是否有活躍交易需要平倉
                trades_to_close = []
                for trade_id, trade_info in self.active_trades.items():
                    if self.check_close_conditions(trade_info):
                        trades_to_close.append(trade_id)
                
                # 執行平倉
                for trade_id in trades_to_close:
                    close_result = self.close_arbitrage_trade(self.active_trades[trade_id])
                    if close_result:
                        # 更新交易記錄
                        trade_info = self.active_trades.pop(trade_id)
                        trade_info.update(close_result)
                        self.trade_history.append(trade_info)
                
                # 檢查新的套利機會
                if not self.active_trades:  # 沒有活躍交易時才開新倉
                    if self.check_arbitrage_conditions(spread_info, funding_rate or 0):
                        print("🚨 發現套利機會！")
                        
                        # 發送套利機會通知
                        self.notifier.send_arbitrage_opportunity(
                            prices['bybit'], prices['mt5'],
                            spread_info['absolute'], spread_info['percentage'],
                            funding_rate or 0
                        )
                        
                        # 計算持倉大小
                        position_sizes = self.calculate_position_sizes(
                            prices['bybit'], prices['mt5']
                        )
                        
                        if position_sizes:
                            # 執行套利交易
                            trade_result = self.execute_arbitrage_trade(
                                spread_info, prices, position_sizes
                            )
                            
                            if trade_result:
                                self.active_trades[trade_result['trade_id']] = trade_result
                
                # 保存交易數據
                self.save_trade_data()
                
                # 等待下次檢查
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️ 套利系統已停止")
        except Exception as e:
            print(f"❌ 套利系統錯誤: {e}")
            self.notifier.send_error_notification(str(e), "套利系統運行")
        finally:
            # 保存最終數據
            self.save_trade_data()

def main():
    trader = XAUArbitrageTrader()
    trader.run_arbitrage_system()

if __name__ == "__main__":
    main() 