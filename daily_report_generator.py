#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日交易报告生成器
"""

import os
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging

from telegram_notifier import TelegramNotifier
from bybit_futures_client import BybitFuturesClient
from cloud_config import get_mt5_client

class DailyReportGenerator:
    def __init__(self):
        # 初始化客户端（自动选择云端兼容版本）
        self.notifier = TelegramNotifier()
        self.bybit_client = BybitFuturesClient()
        MT5ClientClass = get_mt5_client()
        self.mt5_client = MT5ClientClass()
        
        # 数据文件路径
        self.trades_file = 'data/arbitrage_trades.json'
        self.daily_reports_file = 'data/daily_reports.json'
        
        # 确保数据目录存在
        os.makedirs('data', exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('daily_report.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_trades_data(self) -> List[Dict]:
        """加载交易数据"""
        try:
            if os.path.exists(self.trades_file):
                with open(self.trades_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.error(f"加载交易数据失败: {e}")
            return []

    def get_account_balances(self) -> Dict[str, float]:
        """获取当前账户余额"""
        balances = {
            'bybit_balance': 0.0,
            'mt5_balance': 0.0
        }
        
        try:
            # 获取Bybit余额
            bybit_info = self.bybit_client.get_account_info()
            if bybit_info and bybit_info.get('retCode') == 0:
                wallet_list = bybit_info.get('result', {}).get('list', [])
                if wallet_list:
                    balances['bybit_balance'] = float(wallet_list[0].get('totalWalletBalance', 0))
            
            # 获取MT5余额
            if self.mt5_client.connect():
                mt5_info = self.mt5_client.get_account_info()
                if mt5_info:
                    balances['mt5_balance'] = float(mt5_info.get('balance', 0))
                self.mt5_client.disconnect()
                
        except Exception as e:
            self.logger.error(f"获取账户余额失败: {e}")
        
        return balances

    def calculate_daily_stats(self, target_date: str) -> Dict[str, Any]:
        """计算指定日期的交易统计"""
        trades = self.load_trades_data()
        
        # 筛选目标日期的交易
        daily_trades = []
        for trade in trades:
            if isinstance(trade, dict):
                trade_date = trade.get('close_time', trade.get('open_time', ''))[:10]
                if trade_date == target_date:
                    daily_trades.append(trade)
        
        if not daily_trades:
            return self.get_empty_stats(target_date)
        
        # 计算统计数据
        stats = {
            'date': target_date,
            'total_trades': len(daily_trades),
            'successful_trades': len([t for t in daily_trades if t.get('net_profit', 0) > 0]),
            'failed_trades': len([t for t in daily_trades if t.get('net_profit', 0) <= 0]),
            'gross_profit': sum(t.get('gross_profit', 0) for t in daily_trades),
            'total_fees': sum(t.get('total_fees', 0) for t in daily_trades),
            'net_profit': sum(t.get('net_profit', 0) for t in daily_trades),
            'max_profit': max((t.get('net_profit', 0) for t in daily_trades), default=0),
            'min_profit': min((t.get('net_profit', 0) for t in daily_trades), default=0),
            'avg_profit_per_trade': 0,
            'avg_entry_spread': 0,
            'avg_exit_spread': 0,
            'max_spread': 0,
            'risk_triggers': len([t for t in daily_trades if t.get('risk_triggered', False)]),
            'trade_details': self.format_trade_details(daily_trades)
        }
        
        # 计算平均值
        if stats['total_trades'] > 0:
            stats['avg_profit_per_trade'] = stats['net_profit'] / stats['total_trades']
            stats['success_rate'] = (stats['successful_trades'] / stats['total_trades']) * 100
            
            entry_spreads = [t.get('entry_spread', 0) for t in daily_trades if t.get('entry_spread')]
            exit_spreads = [t.get('exit_spread', 0) for t in daily_trades if t.get('exit_spread')]
            
            if entry_spreads:
                stats['avg_entry_spread'] = sum(entry_spreads) / len(entry_spreads)
                stats['max_spread'] = max(entry_spreads)
            
            if exit_spreads:
                stats['avg_exit_spread'] = sum(exit_spreads) / len(exit_spreads)
        
        # 获取账户余额
        current_balances = self.get_account_balances()
        stats.update(current_balances)
        
        return stats

    def get_empty_stats(self, target_date: str) -> Dict[str, Any]:
        """获取空统计数据"""
        current_balances = self.get_account_balances()
        
        return {
            'date': target_date,
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'success_rate': 0,
            'gross_profit': 0,
            'total_fees': 0,
            'net_profit': 0,
            'avg_profit_per_trade': 0,
            'max_profit': 0,
            'min_profit': 0,
            'avg_entry_spread': 0,
            'avg_exit_spread': 0,
            'max_spread': 0,
            'risk_triggers': 0,
            'active_positions': 0,
            'trade_details': '今日無交易記錄',
            **current_balances
        }

    def format_trade_details(self, trades: List[Dict]) -> str:
        """格式化交易明细"""
        if not trades:
            return '今日無交易記錄'
        
        details = []
        for i, trade in enumerate(trades, 1):
            detail = (
                f"{i}. ID: {trade.get('trade_id', 'N/A')[:8]}...\n"
                f"   時間: {trade.get('open_time', 'N/A')[:16]} - {trade.get('close_time', 'N/A')[:16]}\n"
                f"   收益: {trade.get('net_profit', 0):.2f} USD\n"
                f"   價差: {trade.get('entry_spread', 0):.3f}% → {trade.get('exit_spread', 0):.3f}%"
            )
            details.append(detail)
        
        return '\n\n'.join(details)

    def generate_and_send_report(self, target_date: str = None) -> bool:
        """生成并发送每日报告"""
        if target_date is None:
            # 默认生成前一天的报告
            yesterday = datetime.now() - timedelta(days=1)
            target_date = yesterday.strftime('%Y-%m-%d')
        
        self.logger.info(f"生成 {target_date} 的每日报告")
        
        try:
            # 计算统计数据
            stats = self.calculate_daily_stats(target_date)
            
            # 发送报告
            success = self.notifier.send_daily_report(stats)
            
            if success:
                self.logger.info(f"每日报告发送成功: {target_date}")
                # 保存报告记录
                self.save_report_record(target_date, stats)
            else:
                self.logger.error(f"每日报告发送失败: {target_date}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"生成每日报告失败: {e}")
            return False

    def save_report_record(self, date: str, stats: Dict[str, Any]):
        """保存报告记录"""
        try:
            reports = []
            if os.path.exists(self.daily_reports_file):
                with open(self.daily_reports_file, 'r', encoding='utf-8') as f:
                    reports = json.load(f)
            
            # 添加新报告
            report_record = {
                'date': date,
                'generated_at': datetime.now().isoformat(),
                'stats': stats
            }
            reports.append(report_record)
            
            # 只保留最近30天的记录
            reports = reports[-30:]
            
            with open(self.daily_reports_file, 'w', encoding='utf-8') as f:
                json.dump(reports, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存报告记录失败: {e}")

def main():
    """主函数"""
    generator = DailyReportGenerator()
    
    # 生成并发送昨天的报告
    success = generator.generate_and_send_report()
    
    if success:
        print("✅ 每日报告生成并发送成功")
    else:
        print("❌ 每日报告生成或发送失败")

if __name__ == "__main__":
    main()
