"""
詳細分析報告生成器
分析7個交易日的BYBIT XAUTUSDT vs BYBIT MT5 XAUUSD+差價數據
"""
import pandas as pd
import numpy as np
from datetime import datetime

def generate_detailed_report():
    """生成詳細的分析報告"""
    
    # 讀取數據
    try:
        data = pd.read_csv('data/xau_spread_analysis_simple.csv')
        data['time'] = pd.to_datetime(data['time'])
        data['date'] = data['time'].dt.date
        
        print("="*100)
        print("BYBIT XAUTUSDT vs BYBIT MT5 XAUUSD+ 詳細分析報告")
        print("="*100)
        
        # 基本信息
        print(f"\n📊 數據概覽:")
        print(f"總記錄數: {len(data):,} 筆")
        print(f"時間範圍: {data['time'].min()} 到 {data['time'].max()}")
        print(f"交易日數: {len(data['date'].unique())} 天")
        
        # 每日統計
        print(f"\n📅 每日數據統計:")
        print("-"*120)
        print(f"{'日期':<12} {'記錄數':<8} {'最大正差價':<12} {'最小負差價':<12} {'平均差價':<12} {'標準差':<10} {'正差價次數':<10} {'負差價次數':<10}")
        print("-"*120)
        
        daily_stats = []
        for date in sorted(data['date'].unique()):
            daily_data = data[data['date'] == date]
            spread_pct = daily_data['price_diff_pct']
            
            max_pos = spread_pct.max()
            min_neg = spread_pct.min()
            mean_spread = spread_pct.mean()
            std_spread = spread_pct.std()
            pos_count = len(spread_pct[spread_pct > 0])
            neg_count = len(spread_pct[spread_pct < 0])
            
            daily_stats.append({
                'date': date,
                'records': len(daily_data),
                'max_positive': max_pos,
                'min_negative': min_neg,
                'mean_spread': mean_spread,
                'std_spread': std_spread,
                'positive_count': pos_count,
                'negative_count': neg_count
            })
            
            print(f"{date} {len(daily_data):>7} {max_pos:>11.6f}% {min_neg:>11.6f}% {mean_spread:>11.6f}% {std_spread:>9.6f}% {pos_count:>9} {neg_count:>9}")
        
        # 整體統計
        spread_pct = data['price_diff_pct']
        print(f"\n📈 整體統計:")
        print(f"最大正差價: {spread_pct.max():.6f}%")
        print(f"最小負差價: {spread_pct.min():.6f}%")
        print(f"平均差價: {spread_pct.mean():.6f}%")
        print(f"標準差: {spread_pct.std():.6f}%")
        print(f"中位數: {spread_pct.median():.6f}%")
        
        # 方向分布
        positive_spreads = spread_pct[spread_pct > 0]
        negative_spreads = spread_pct[spread_pct < 0]
        zero_spreads = spread_pct[spread_pct == 0]
        
        print(f"\n🎯 差價方向分布:")
        print(f"正差價 (Bybit > MT5): {len(positive_spreads):,} 次 ({len(positive_spreads)/len(spread_pct)*100:.2f}%)")
        print(f"負差價 (Bybit < MT5): {len(negative_spreads):,} 次 ({len(negative_spreads)/len(spread_pct)*100:.2f}%)")
        print(f"零差價: {len(zero_spreads):,} 次 ({len(zero_spreads)/len(spread_pct)*100:.2f}%)")
        
        # 套利機會分析
        print(f"\n💰 套利機會分析 (不同閾值):")
        print("-"*80)
        print(f"{'閾值':<10} {'正差價機會':<12} {'負差價機會':<12} {'總機會':<10} {'佔比':<8} {'每日平均':<10}")
        print("-"*80)
        
        thresholds = [0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5]
        trading_days = len(data['date'].unique())
        
        for threshold in thresholds:
            pos_count = len(positive_spreads[positive_spreads >= threshold])
            neg_count = len(negative_spreads[negative_spreads <= -threshold])
            total_count = pos_count + neg_count
            percentage = total_count / len(spread_pct) * 100
            daily_avg = total_count / trading_days
            
            print(f"{threshold:.3f}%{'':<4} {pos_count:<12} {neg_count:<12} {total_count:<10} {percentage:>6.2f}% {daily_avg:>9.1f}")
        
        # 價格範圍分析
        print(f"\n💵 價格範圍分析:")
        print(f"Bybit XAUTUSDT 價格範圍: ${data['bybit_price'].min():.2f} - ${data['bybit_price'].max():.2f}")
        print(f"MT5 XAUUSD+ 價格範圍: ${data['mt5_price'].min():.2f} - ${data['mt5_price'].max():.2f}")
        print(f"平均Bybit價格: ${data['bybit_price'].mean():.2f}")
        print(f"平均MT5價格: ${data['mt5_price'].mean():.2f}")
        
        # 時段分析
        data['hour'] = data['time'].dt.hour
        hourly_stats = data.groupby('hour')['price_diff_pct'].agg(['count', 'mean', 'std']).round(6)
        
        print(f"\n⏰ 時段分析 (按小時):")
        print("-"*60)
        print(f"{'時段':<6} {'記錄數':<8} {'平均差價%':<12} {'標準差%':<10}")
        print("-"*60)
        
        for hour in range(24):
            if hour in hourly_stats.index:
                count = hourly_stats.loc[hour, 'count']
                mean_val = hourly_stats.loc[hour, 'mean']
                std_val = hourly_stats.loc[hour, 'std']
                print(f"{hour:02d}:00 {count:>7} {mean_val:>11.6f} {std_val:>9.6f}")
        
        # 保存詳細統計
        daily_df = pd.DataFrame(daily_stats)
        daily_df.to_csv('data/daily_spread_statistics.csv', index=False)
        
        hourly_stats.to_csv('data/hourly_spread_statistics.csv')
        
        print(f"\n✅ 詳細統計已保存:")
        print(f"   - data/daily_spread_statistics.csv (每日統計)")
        print(f"   - data/hourly_spread_statistics.csv (時段統計)")
        
        # 結論
        print(f"\n🎯 關鍵結論:")
        print(f"1. 在7個交易日中共有 {len(data):,} 筆有效價格對比數據")
        print(f"2. Bybit價格平均比MT5低 {abs(spread_pct.mean()):.3f}%")
        print(f"3. 在0.02%閾值下，每日平均有 {(len(positive_spreads[positive_spreads >= 0.02]) + len(negative_spreads[negative_spreads <= -0.02]))/trading_days:.0f} 次套利機會")
        print(f"4. 最大套利機會為 {max(abs(spread_pct.max()), abs(spread_pct.min())):.3f}%")
        print(f"5. 差價波動標準差為 {spread_pct.std():.3f}%，顯示有穩定的套利空間")
        
    except FileNotFoundError:
        print("❌ 找不到數據文件，請先運行 phase1_simple_analysis.py")
    except Exception as e:
        print(f"❌ 生成報告時發生錯誤: {e}")

if __name__ == "__main__":
    generate_detailed_report()
